/**
 * CSP 违规报告处理器
 * 收集和分析 CSP 违规报告
 */

(function() {
    'use strict';
    
    // CSP 违规报告收集器
    const CSPReporter = {
        violations: [],
        maxViolations: 100,
        
        // 初始化报告收集
        init: function() {
            // 监听 CSP 违规事件
            document.addEventListener('securitypolicyviolation', (e) => {
                this.handleViolation(e);
            });
            
            console.log('🔒 CSP 违规报告收集器已启动');
        },
        
        // 处理违规事件
        handleViolation: function(event) {
            const violation = {
                directive: event.violatedDirective,
                blockedURI: event.blockedURI,
                lineNumber: event.lineNumber,
                columnNumber: event.columnNumber,
                sourceFile: event.sourceFile,
                sample: event.sample,
                timestamp: new Date().toISOString()
            };
            
            this.violations.push(violation);
            
            // 限制违规记录数量
            if (this.violations.length > this.maxViolations) {
                this.violations.shift();
            }
            
            // 输出到控制台
            console.warn('🚨 CSP 违规:', violation);
            
            // 可选：发送到服务器
            // this.sendToServer(violation);
        },
        
        // 获取违规报告
        getReport: function() {
            return {
                totalViolations: this.violations.length,
                violations: this.violations,
                summary: this.generateSummary()
            };
        },
        
        // 生成违规摘要
        generateSummary: function() {
            const summary = {};
            
            this.violations.forEach(v => {
                const key = v.directive;
                summary[key] = (summary[key] || 0) + 1;
            });
            
            return summary;
        },
        
        // 清除违规记录
        clear: function() {
            this.violations = [];
            console.log('🧹 CSP 违规记录已清除');
        },
        
        // 发送到服务器（可选）
        sendToServer: function(violation) {
            // 这里可以实现发送违规报告到服务器的逻辑
            // fetch('/api/csp-report', {
            //     method: 'POST',
            //     headers: { 'Content-Type': 'application/json' },
            //     body: JSON.stringify(violation)
            // });
        }
    };
    
    // 初始化
    CSPReporter.init();
    
    // 暴露到全局
    window.CSPReporter = CSPReporter;
    
    // 添加调试命令
    window.getCSPReport = function() {
        const report = CSPReporter.getReport();
        console.log('📊 CSP 违规报告:', report);
        return report;
    };
    
})();