/**
 * 食材溯源功能JavaScript
 * 包含溯源链生成函数和辅助函数
 */

// 生成菜单计划溯源链
function generateMenuPlanTrace(data, container) {
    // 跟踪是否生成了任何溯源链节点
    var hasGeneratedChain = false;
    var hasDownstreamData = false;

    // 1. 周菜单（起点） - 日菜单功能已移除
    if (data.weekly_menu) {
        hasGeneratedChain = true;
        var html = `
            <div class="trace-step">
                <div class="trace-icon bg-primary">
                    <i class="fas fa-utensils"></i>
                </div>
                <div class="trace-content">
                    <h5>周菜单 <span class="badge badge-${getStatusClass(data.weekly_menu.status)}">${data.weekly_menu.status}</span></h5>
                    <div class="trace-card">
                        <p><strong>ID:</strong> ${data.weekly_menu.id}</p>
                        <p><strong>周期:</strong> ${data.weekly_menu.week_start} 至 ${data.weekly_menu.week_end}</p>
                        <p><strong>区域:</strong> ${data.weekly_menu.area_name || '未知'}</p>
                        <p><strong>创建人:</strong> ${data.weekly_menu.creator || '未知'}</p>
                    </div>
                </div>
            </div>
        `;
        container.append(html);
    } else {
        // 如果没有周菜单数据，显示断链提示
        container.append(`
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-circle"></i> 溯源起点缺失</h5>
                <p>未能获取到菜单计划数据，溯源链无法建立。</p>
            </div>
        `);
        return false;
    }

    // 2. 消耗计划（下游）
    if (data.consumption_plans && data.consumption_plans.length > 0) {
        hasDownstreamData = true;
        var html = `
            <div class="trace-step">
                <div class="trace-icon bg-info">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <div class="trace-content">
                    <h5>消耗计划</h5>
        `;

        data.consumption_plans.forEach(function(plan) {
            html += `
                <div class="trace-card">
                    <p><strong>ID:</strong> ${plan.id}</p>
                    <p><strong>创建时间:</strong> ${plan.created_at}</p>
                    <p><strong>状态:</strong> <span class="badge badge-${getStatusClass(plan.status)}">${plan.status}</span></p>
                    <p><strong>创建人:</strong> ${plan.creator || '未知'}</p>
                </div>
            `;
        });

        html += `
                </div>
            </div>
        `;
        container.append(html);
    } else {
        // 如果没有消耗计划数据，显示下游断链提示
        container.append(`
            <div class="alert alert-warning">
                <h5><i class="fas fa-exclamation-triangle"></i> 下游溯源断链</h5>
                <p>未能获取到消耗计划数据，下游溯源链不完整。</p>
                <p>这可能是因为该菜单计划尚未生成消耗计划。</p>
            </div>
        `);
    }

    // 返回是否成功生成溯源链
    return hasGeneratedChain && hasDownstreamData;
}

// 生成出库单溯源链
function generateStockOutTrace(data, container) {
    // 跟踪是否生成了任何溯源链节点
    var hasGeneratedChain = false;
    var hasUpstreamData = false;

    // 1. 出库单（起点）
    if (data.stock_out) {
        hasGeneratedChain = true;
        var html = `
            <div class="trace-step">
                <div class="trace-icon bg-warning">
                    <i class="fas fa-dolly"></i>
                </div>
                <div class="trace-content">
                    <h5>出库单 <span class="badge badge-${getStatusClass(data.stock_out.status)}">${data.stock_out.status}</span></h5>
                    <div class="trace-card">
                        <p><strong>ID:</strong> ${data.stock_out.id}</p>
                        <p><strong>出库单号:</strong> ${data.stock_out.stock_out_number}</p>
                        <p><strong>出库日期:</strong> ${data.stock_out.stock_out_date}</p>
                        <p><strong>类型:</strong> ${data.stock_out.stock_out_type}</p>
                        <p><strong>操作人:</strong> ${data.stock_out.operator || '未知'}</p>
                    </div>
                </div>
            </div>
        `;
        container.append(html);
    } else {
        // 如果没有出库单数据，显示断链提示
        container.append(`
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-circle"></i> 溯源起点缺失</h5>
                <p>未能获取到出库单数据，溯源链无法建立。</p>
            </div>
        `);
        return false;
    }

    // 2. 消耗计划（上游）
    if (data.consumption_plan) {
        hasUpstreamData = true;
        var html = `
            <div class="trace-step">
                <div class="trace-icon bg-info">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <div class="trace-content">
                    <h5>消耗计划 <span class="badge badge-${getStatusClass(data.consumption_plan.status)}">${data.consumption_plan.status}</span></h5>
                    <div class="trace-card">
                        <p><strong>ID:</strong> ${data.consumption_plan.id}</p>
                        <p><strong>创建时间:</strong> ${data.consumption_plan.created_at}</p>
                        <p><strong>创建人:</strong> ${data.consumption_plan.creator || '未知'}</p>
                    </div>
                </div>
            </div>
        `;
        container.append(html);

        // 3. 菜单计划（更上游）
        if (data.menu_plan) {
            var html = `
                <div class="trace-step">
                    <div class="trace-icon bg-primary">
                        <i class="fas fa-utensils"></i>
                    </div>
                    <div class="trace-content">
                        <h5>菜单计划 <span class="badge badge-${getStatusClass(data.menu_plan.status)}">${data.menu_plan.status}</span></h5>
                        <div class="trace-card">
                            <p><strong>ID:</strong> ${data.menu_plan.id}</p>
                            <p><strong>日期:</strong> ${data.menu_plan.plan_date}</p>
                            <p><strong>餐次:</strong> ${data.menu_plan.meal_type}</p>
                            <p><strong>区域:</strong> ${data.menu_plan.area_name || '未知'}</p>
                        </div>
                    </div>
                </div>
            `;
            container.append(html);
        } else {
            // 如果没有菜单计划数据，显示上游断链提示
            container.append(`
                <div class="alert alert-warning">
                    <h5><i class="fas fa-exclamation-triangle"></i> 上游溯源断链</h5>
                    <p>未能获取到菜单计划数据，上游溯源链不完整。</p>
                    <p>这可能是因为该消耗计划不是基于菜单计划创建的。</p>
                </div>
            `);
        }
    } else {
        // 如果没有消耗计划数据，显示上游断链提示
        container.append(`
            <div class="alert alert-warning">
                <h5><i class="fas fa-exclamation-triangle"></i> 上游溯源断链</h5>
                <p>未能获取到消耗计划数据，上游溯源链不完整。</p>
                <p>这可能是因为该出库单不是基于消耗计划创建的。</p>
            </div>
        `);
    }

    // 4. 库存和入库信息（最终来源）
    if (data.inventory_data && data.inventory_data.length > 0) {
        var html = `
            <div class="trace-step">
                <div class="trace-icon bg-success">
                    <i class="fas fa-warehouse"></i>
                </div>
                <div class="trace-content">
                    <h5>库存和入库信息</h5>
                    <div class="trace-card">
                        <table class="trace-table">
                            <thead>
                                <tr>
                                    <th>批次号</th>
                                    <th>食材</th>
                                    <th>数量</th>
                                    <th>入库单号</th>
                                    <th>入库日期</th>
                                    <th>供应商</th>
                                    <th>生产日期</th>
                                    <th>过期日期</th>
                                </tr>
                            </thead>
                            <tbody>
        `;

        data.inventory_data.forEach(function(item) {
            html += `
                <tr>
                    <td>${item.batch_number || '未知'}</td>
                    <td>${item.ingredient_name || '未知'}</td>
                    <td>${item.quantity} ${item.unit}</td>
                    <td>${item.stock_in_number || '未知'}</td>
                    <td>${item.stock_in_date || '未知'}</td>
                    <td>${item.supplier_name || '未知'}</td>
                    <td>${item.production_date || '未知'}</td>
                    <td>${item.expiry_date || '未知'}</td>
                </tr>
            `;
        });

        html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
        container.append(html);
    } else {
        // 如果没有库存数据，显示最终来源断链提示
        container.append(`
            <div class="alert alert-warning">
                <h5><i class="fas fa-exclamation-triangle"></i> 最终来源溯源断链</h5>
                <p>未能获取到库存和入库信息，无法追溯到食材的最终来源。</p>
                <p>这可能是因为相关库存记录已被删除或数据不完整。</p>
            </div>
        `);
    }

    // 返回是否成功生成溯源链
    return hasGeneratedChain && hasUpstreamData;
}

// 生成食材溯源链
function generateIngredientTrace(data, container) {
    // 跟踪是否生成了任何溯源链节点
    var hasGeneratedChain = false;

    // 1. 食材信息（起点）
    if (data.ingredient) {
        hasGeneratedChain = true;
        var html = `
            <div class="trace-step">
                <div class="trace-icon bg-success">
                    <i class="fas fa-carrot"></i>
                </div>
                <div class="trace-content">
                    <h5>食材信息</h5>
                    <div class="trace-card">
                        <p><strong>ID:</strong> ${data.ingredient.id}</p>
                        <p><strong>名称:</strong> ${data.ingredient.name}</p>
                        <p><strong>类别:</strong> ${data.ingredient.category || '未知'}</p>
                        <p><strong>单位:</strong> ${data.ingredient.standard_unit}</p>
                    </div>
                </div>
            </div>
        `;
        container.append(html);
    } else {
        // 如果没有食材数据，显示断链提示
        container.append(`
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-circle"></i> 溯源起点缺失</h5>
                <p>未能获取到食材数据，溯源链无法建立。</p>
            </div>
        `);
        return false;
    }

    // 2. 库存和入库信息
    if (data.inventories && data.inventories.length > 0) {
        var html = `
            <div class="trace-step">
                <div class="trace-icon bg-info">
                    <i class="fas fa-warehouse"></i>
                </div>
                <div class="trace-content">
                    <h5>库存信息</h5>
                    <div class="trace-card">
                        <table class="trace-table">
                            <thead>
                                <tr>
                                    <th>批次号</th>
                                    <th>仓库</th>
                                    <th>数量</th>
                                    <th>单位</th>
                                    <th>状态</th>
                                    <th>生产日期</th>
                                    <th>过期日期</th>
                                </tr>
                            </thead>
                            <tbody>
        `;

        data.inventories.forEach(function(item) {
            html += `
                <tr>
                    <td>${item.batch_number || '未知'}</td>
                    <td>${item.warehouse_name || '未知'}</td>
                    <td>${item.quantity}</td>
                    <td>${item.unit}</td>
                    <td><span class="badge badge-${getStatusClass(item.status)}">${item.status}</span></td>
                    <td>${item.production_date || '未知'}</td>
                    <td>${item.expiry_date || '未知'}</td>
                </tr>
            `;
        });

        html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
        container.append(html);
    } else {
        // 如果没有库存数据，显示断链提示
        container.append(`
            <div class="alert alert-warning">
                <h5><i class="fas fa-exclamation-triangle"></i> 库存溯源断链</h5>
                <p>未能获取到该食材的库存信息。</p>
                <p>这可能是因为该食材当前没有库存，或者相关记录已被删除。</p>
            </div>
        `);
    }

    // 3. 入库记录
    if (data.stock_in_items && data.stock_in_items.length > 0) {
        var html = `
            <div class="trace-step">
                <div class="trace-icon bg-primary">
                    <i class="fas fa-truck-loading"></i>
                </div>
                <div class="trace-content">
                    <h5>入库记录</h5>
                    <div class="trace-card">
                        <table class="trace-table">
                            <thead>
                                <tr>
                                    <th>入库单号</th>
                                    <th>入库日期</th>
                                    <th>批次号</th>
                                    <th>数量</th>
                                    <th>供应商</th>
                                    <th>生产日期</th>
                                    <th>过期日期</th>
                                    <th>质检结果</th>
                                </tr>
                            </thead>
                            <tbody>
        `;

        data.stock_in_items.forEach(function(item) {
            html += `
                <tr>
                    <td>${item.stock_in_number || '未知'}</td>
                    <td>${item.stock_in_date || '未知'}</td>
                    <td>${item.batch_number || '未知'}</td>
                    <td>${item.quantity} ${item.unit}</td>
                    <td>${item.supplier_name || '未知'}</td>
                    <td>${item.production_date || '未知'}</td>
                    <td>${item.expiry_date || '未知'}</td>
                    <td>${item.quality_check_result || '未知'}</td>
                </tr>
            `;
        });

        html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
        container.append(html);
    } else {
        // 如果没有入库记录，显示提示
        container.append(`
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle"></i> 无入库记录</h5>
                <p>未找到该食材的入库记录。</p>
                <p>这可能是因为相关记录已被删除或数据不完整。</p>
            </div>
        `);
    }

    // 4. 消耗记录
    if (data.consumption_details && data.consumption_details.length > 0) {
        var html = `
            <div class="trace-step">
                <div class="trace-icon bg-warning">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <div class="trace-content">
                    <h5>消耗记录</h5>
                    <div class="trace-card">
                        <table class="trace-table">
                            <thead>
                                <tr>
                                    <th>消耗计划ID</th>
                                    <th>菜单日期</th>
                                    <th>餐次</th>
                                    <th>计划数量</th>
                                    <th>实际数量</th>
                                    <th>单位</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
        `;

        data.consumption_details.forEach(function(detail) {
            html += `
                <tr>
                    <td>${detail.consumption_plan_id}</td>
                    <td>${detail.menu_plan_date || '未知'}</td>
                    <td>${detail.menu_plan_meal_type || '未知'}</td>
                    <td>${detail.planned_quantity}</td>
                    <td>${detail.actual_quantity || '未执行'}</td>
                    <td>${detail.unit}</td>
                    <td><span class="badge badge-${getStatusClass(detail.status)}">${detail.status}</span></td>
                </tr>
            `;
        });

        html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
        container.append(html);
    } else {
        // 如果没有消耗记录，显示提示
        container.append(`
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle"></i> 无消耗记录</h5>
                <p>未找到该食材的消耗记录。</p>
                <p>这可能是因为该食材尚未被消耗，或者相关记录已被删除。</p>
            </div>
        `);
    }

    // 返回是否成功生成溯源链
    return hasGeneratedChain;
}

// 根据状态获取对应的Bootstrap样式类
function getStatusClass(status) {
    switch(status) {
        case '计划中':
            return 'secondary';
        case '已审核':
            return 'info';
        case '已执行':
            return 'success';
        case '已取消':
            return 'danger';
        case '已发布':
            return 'danger'; // 改为红色，更加醒目
        case '待审核':
            return 'warning';
        case '已出库':
            return 'success';
        case '正常':
            return 'success';
        case '过期':
            return 'danger';
        case '即将过期':
            return 'warning';
        default:
            return 'secondary';
    }
}
