@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义样式 */
@layer components {
  /* 这里可以添加自定义组件样式 */
}

/* 全局表格样式优化 - 专业版 */

/* 表头样式 */
.table th,
.table thead th,
.table-sm thead th,
.table-bordered thead th,
.table-striped thead th,
.table-hover thead th,
.thead-dark th,
.thead-light th,
table thead th,
table th {
    font-weight: normal !important;
    font-size: 0.875rem !important;
    color: #6c757d !important;
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #dee2e6 !important;
    padding: 0.75rem 0.5rem !important;
    vertical-align: middle !important;
    text-transform: none !important;
    letter-spacing: normal !important;
}

/* 表格内容样式 - 全部使用普通字体 */
.table td,
.table tbody td,
.table-sm td,
.table-sm tbody td,
.table-bordered td,
.table-striped td,
.table-hover td,
table td,
table tbody td {
    font-weight: normal !important;
    font-size: 0.875rem !important;
    padding: 0.75rem 0.5rem !important;
    vertical-align: middle !important;
    border-top: 1px solid #e9ecef !important;
}

/* 表格内的链接也使用普通字体 */
.table td a,
.table tbody td a,
.table-sm td a,
.table-sm tbody td a,
table td a,
table tbody td a {
    font-weight: normal !important;
    text-decoration: none;
}

/* 表格内的强调文本也使用普通字体 */
.table td strong,
.table td b,
.table tbody td strong,
.table tbody td b,
.table-sm td strong,
.table-sm td b,
table td strong,
table td b {
    font-weight: 500 !important; /* 稍微加粗但不是黑体 */
}

/* 深色表头保持原有颜色但使用普通字体 */
.thead-dark th {
    background-color: #343a40 !important;
    color: #fff !important;
    border-color: #454d55 !important;
    font-weight: normal !important;
}

/* 表格行样式优化 */
.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* 数字列专用样式 */
.table .number-column,
.table .amount-column {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    font-weight: normal !important;
    text-align: right;
}

/* 状态列样式 */
.table .status-column {
    font-weight: normal !important;
}

/* 全局强制普通字体样式 - 覆盖所有可能的黑体 */
.table *,
.table-sm *,
.table-bordered *,
.table-striped *,
.table-hover *,
table * {
    font-weight: normal !important;
}

/* 特殊情况：稍微加粗但不是黑体 */
.table .semi-bold,
.table .medium-weight {
    font-weight: 500 !important;
}

/* Bootstrap徽章样式覆盖 */
.badge,
.badge-primary,
.badge-secondary,
.badge-success,
.badge-danger,
.badge-warning,
.badge-info,
.badge-light,
.badge-dark {
    font-weight: normal !important;
}

/* 按钮样式覆盖 */
.btn,
.btn-primary,
.btn-secondary,
.btn-success,
.btn-danger,
.btn-warning,
.btn-info,
.btn-light,
.btn-dark,
.btn-outline-primary,
.btn-outline-secondary,
.btn-outline-success,
.btn-outline-danger,
.btn-outline-warning,
.btn-outline-info,
.btn-outline-light,
.btn-outline-dark {
    font-weight: normal !important;
}

/* 链接样式覆盖 */
a, a:hover, a:focus, a:active {
    font-weight: normal !important;
}

/* 表单控件样式覆盖 */
.form-control,
.form-select,
.form-check-label,
.form-label,
label {
    font-weight: normal !important;
}