/* 优雅导航栏样式 */

/* === 导航栏基础优化 === */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
    letter-spacing: 0.5px;
}

/* === 导航菜单优化 === */
.navbar-nav .nav-item {
    margin: 0 2px;
}

.navbar-nav .nav-link {
    padding: 0.75rem 1rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-weight: 500;
    position: relative;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.15);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* === 下拉菜单优雅化 === */
.dropdown-menu {
    border: none;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    padding: 8px 0;
    margin-top: 8px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    min-width: 200px;
    max-width: 250px;
    width: auto;
    max-height: 70vh;
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    box-sizing: border-box;
}

.dropdown-menu::before {
    content: '';
    position: absolute;
    top: -6px;
    left: 20px;
    width: 12px;
    height: 12px;
    background: rgba(255, 255, 255, 0.95);
    transform: rotate(45deg);
    border-radius: 2px;
}

/* === 下拉菜单项优化 === */
.dropdown-item {
    padding: 8px 16px;
    border-radius: 6px;
    margin: 1px 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    font-size: 15px;
    color: #495057;
    border: none;
    white-space: nowrap;
}

.dropdown-item:hover,
.dropdown-item:focus {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white !important;
    transform: translateX(2px);
    box-shadow: 0 2px 6px rgba(0, 123, 255, 0.25);
    border-radius: 6px;
    margin: 1px 6px 1px 6px;
    padding: 8px 16px;
    max-width: calc(100% - 12px);
    box-sizing: border-box;
}

/* 活跃状态样式 */
.dropdown-item.active,
.dropdown-item:active {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    color: white !important;
    transform: translateX(1px);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    margin: 1px 6px 1px 6px;
    padding: 8px 16px;
    max-width: calc(100% - 12px);
    box-sizing: border-box;
}

.dropdown-item i {
    width: 16px;
    margin-right: 8px;
    text-align: center;
    font-size: 12px;
    flex-shrink: 0;
}

/* === 分组标题样式 === */
.dropdown-header {
    padding: 6px 16px 3px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #6c757d;
    border-bottom: 1px solid #e9ecef;
    margin: 3px 6px 6px;
    background: none;
}

.dropdown-header:first-child {
    margin-top: 0;
}

/* === 分割线优化 === */
.dropdown-divider {
    margin: 8px 16px;
    border-top: 1px solid #e9ecef;
    opacity: 0.5;
}

/* === 特殊菜单项样式 === */
.dropdown-item.menu-header {
    background: #f8f9fa;
    color: #6c757d;
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: default;
    padding: 6px 20px;
    margin: 4px 8px;
    border-radius: 4px;
}

.dropdown-item.menu-header:hover {
    background: #f8f9fa;
    color: #6c757d;
    transform: none;
    box-shadow: none;
}

/* === 图标优化 === */
.nav-link i {
    margin-right: 6px;
    font-size: 14px;
}

.dropdown-item i {
    color: inherit;
    opacity: 0.8;
}

.dropdown-item:hover i,
.dropdown-item:focus i,
.dropdown-item.active i {
    opacity: 1;
    color: white;
}

/* === 徽章和通知 === */
.notification-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: 10px;
    padding: 2px 5px;
    border-radius: 10px;
    min-width: 16px;
    height: 16px;
    line-height: 12px;
    text-align: center;
}

/* === 用户菜单优化 === */
.navbar-nav .dropdown-toggle::after {
    margin-left: 6px;
    font-size: 12px;
}

/* === 主题切换器优化 === */
.theme-preview {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    margin-right: 8px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    display: inline-block;
}

.dropdown-item.theme-option {
    font-size: 13px;
}

.dropdown-item.theme-option:hover .theme-preview {
    border-color: white;
    transform: scale(1.1);
}

/* === 移动端优化 === */
@media (max-width: 768px) {
    .navbar-nav .nav-link {
        padding: 12px 16px;
        margin: 2px 0;
        border-radius: 8px;
    }

    .dropdown-menu {
        position: static !important;
        float: none !important;
        width: 100% !important;
        margin-top: 0 !important;
        background-color: rgba(0, 0, 0, 0.05) !important;
        border: none !important;
        border-radius: 0 !important;
        box-shadow: none !important;
        padding: 0 !important;
        backdrop-filter: none;
        -webkit-backdrop-filter: none;
    }

    .dropdown-menu::before {
        display: none;
    }

    .dropdown-item {
        padding: 12px 24px;
        margin: 0;
        border-radius: 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.9);
        width: 100% !important;
        max-width: none !important;
        display: block;
        text-decoration: none;
        cursor: pointer;
        -webkit-tap-highlight-color: rgba(255, 255, 255, 0.1);
    }

    .dropdown-item:hover,
    .dropdown-item:focus,
    .dropdown-item:active {
        background: rgba(255, 255, 255, 0.1) !important;
        color: white !important;
        transform: none;
        box-shadow: none;
        text-decoration: none;
    }

    .dropdown-item:visited {
        color: rgba(255, 255, 255, 0.9);
    }

    .dropdown-header {
        color: rgba(255, 255, 255, 0.7);
        border-bottom-color: rgba(255, 255, 255, 0.1);
        margin: 0;
        padding: 8px 24px 4px;
    }

    .dropdown-divider {
        border-top-color: rgba(255, 255, 255, 0.1);
        margin: 0;
    }

    /* 移动端触摸优化 */
    .navbar-nav .dropdown-toggle {
        cursor: pointer;
        -webkit-tap-highlight-color: rgba(255, 255, 255, 0.1);
    }

    .dropdown-menu {
        -webkit-overflow-scrolling: touch;
        touch-action: manipulation;
    }

    /* 确保移动端下拉菜单可点击 */
    .dropdown-item {
        touch-action: manipulation;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
    }

    /* 移动端下拉菜单触发器优化 */
    .navbar-nav .dropdown-toggle {
        position: relative;
        z-index: 1000;
    }

    .navbar-nav .dropdown-toggle::after {
        margin-left: 8px;
        font-size: 12px;
    }

    /* 移动端下拉菜单显示优化 */
    .navbar-nav .dropdown.show .dropdown-menu {
        display: block !important;
        opacity: 1;
        visibility: visible;
    }

    /* 移动端触摸反馈 */
    .navbar-nav .dropdown-toggle.touch-active {
        background-color: rgba(255, 255, 255, 0.1);
        transform: scale(0.98);
        transition: all 0.1s ease;
    }

    /* 移动端下拉菜单特殊样式 */
    .navbar-nav .dropdown-menu.mobile-dropdown {
        position: static !important;
        float: none !important;
        width: 100% !important;
        margin-top: 0 !important;
        background-color: rgba(0, 0, 0, 0.05) !important;
        border: none !important;
        border-radius: 0 !important;
        box-shadow: none !important;
        backdrop-filter: none !important;
        -webkit-backdrop-filter: none !important;
    }

    .navbar-nav .dropdown-menu.mobile-dropdown::before {
        display: none !important;
    }

    .navbar-nav .dropdown-menu.mobile-dropdown .dropdown-item {
        padding: 12px 24px !important;
        margin: 0 !important;
        border-radius: 0 !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
        color: rgba(255, 255, 255, 0.9) !important;
        width: 100% !important;
        max-width: none !important;
    }

    .navbar-nav .dropdown-menu.mobile-dropdown .dropdown-item:hover,
    .navbar-nav .dropdown-menu.mobile-dropdown .dropdown-item:focus {
        background: rgba(255, 255, 255, 0.1) !important;
        color: white !important;
        transform: none !important;
        box-shadow: none !important;
    }

    .navbar-nav .dropdown-menu.mobile-dropdown .dropdown-header {
        color: rgba(255, 255, 255, 0.7) !important;
        border-bottom-color: rgba(255, 255, 255, 0.1) !important;
        margin: 0 !important;
        padding: 8px 24px 4px !important;
    }
}

/* === 动画效果 === */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-menu.show {
    animation: slideDown 0.3s ease;
}

/* === 滚动条优化 === */
.dropdown-menu::-webkit-scrollbar {
    width: 6px;
}

.dropdown-menu::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
}

.dropdown-menu::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

/* === 活跃状态指示 === */
.navbar-nav .nav-item.active .nav-link {
    background-color: rgba(255, 255, 255, 0.15);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.navbar-nav .nav-item.active .nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 1px;
}

/* === 加载状态 === */
.nav-link.loading {
    opacity: 0.6;
    pointer-events: none;
}

.nav-link.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 8px;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

/* === 紧凑模式 === */
.navbar.compact .navbar-nav .nav-link {
    padding: 0.5rem 0.75rem;
    font-size: 14px;
}

.navbar.compact .dropdown-menu {
    min-width: 180px;
    max-width: 220px;
}

.navbar.compact .dropdown-item {
    padding: 6px 12px;
    font-size: 14px;
}

/* === 自适应宽度优化 === */
.dropdown-menu {
    width: fit-content !important;
}

/* === 特定菜单项优化 === */
.dropdown-item {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* === 长文本处理 === */
.dropdown-item span {
    display: inline-block;
    max-width: 160px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* === 菜单项边界修复 === */
.dropdown-item {
    box-sizing: border-box;
    position: relative;
    overflow: hidden;
    width: calc(100% - 12px);
    margin-left: 6px;
    margin-right: 6px;
}

.dropdown-item:hover,
.dropdown-item:focus {
    background-clip: padding-box;
    border: none;
    outline: none;
    width: calc(100% - 12px);
    margin-left: 6px;
    margin-right: 6px;
}

/* === 精确边界控制 === */
.dropdown-menu .dropdown-item {
    display: block;
    width: calc(100% - 12px);
    margin: 1px 6px;
    padding: 8px 16px;
    clear: both;
    font-weight: 400;
    text-align: inherit;
    text-decoration: none;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
    border-radius: 6px;
    box-sizing: border-box;
}

/* === 高对比度优化 === */
.dropdown-item:hover,
.dropdown-item:focus,
.dropdown-item.active {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* === 确保文字可读性 === */
.dropdown-item:hover *,
.dropdown-item:focus *,
.dropdown-item.active * {
    color: white !important;
}
