{% extends "financial/base.html" %}

{% block title %}利润表{% endblock %}

{% block financial_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">利润表</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('financial.reports_index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回报表列表
                        </a>
                        <button class="btn btn-success btn-sm" onclick="exportReport()">
                            <i class="fas fa-download"></i> 导出Excel
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 报表参数 -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="start_date">开始日期</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" 
                                           value="{{ start_date }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="end_date">结束日期</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" 
                                           value="{{ end_date }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-info btn-sm">
                                            <i class="fas fa-sync"></i> 刷新报表
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 利润表 -->
                    <div class="row">
                        <div class="col-md-10 offset-md-1">
                            <table class="table table-bordered">
                                <thead class="thead-light">
                                    <tr>
                                        <th>项目</th>
                                        <th class="text-right">本期金额</th>
                                        <th class="text-right">上期金额</th>
                                        <th class="text-right">变动金额</th>
                                        <th class="text-right">变动率(%)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 直接成本 -->
                                    <tr class="table-info">
                                        <td><strong>一、直接成本</strong></td>
                                        <td class="text-right"><strong>{{ "%.2f"|format(cost_analysis.direct_cost_total) }}</strong></td>
                                        <td class="text-right"><strong>{{ "%.2f"|format(cost_analysis.direct_cost_total_last) }}</strong></td>
                                        <td class="text-right"><strong>{{ "%.2f"|format(cost_analysis.direct_cost_change) }}</strong></td>
                                        <td class="text-right"><strong>{{ "%.1f"|format(cost_analysis.direct_cost_change_rate) }}%</strong></td>
                                    </tr>

                                    <!-- 食材成本明细 -->
                                    {% for item in cost_analysis.direct_cost_details %}
                                    <tr>
                                        <td>&nbsp;&nbsp;{{ item.name }}</td>
                                        <td class="text-right">{{ "%.2f"|format(item.amount) }}</td>
                                        <td class="text-right">{{ "%.2f"|format(item.amount_last) }}</td>
                                        <td class="text-right">{{ "%.2f"|format(item.change) }}</td>
                                        <td class="text-right">{{ "%.1f"|format(item.change_rate) }}%</td>
                                    </tr>
                                    {% endfor %}

                                    <!-- 间接成本 -->
                                    <tr class="table-warning">
                                        <td><strong>二、间接成本</strong></td>
                                        <td class="text-right"><strong>{{ "%.2f"|format(cost_analysis.indirect_cost_total) }}</strong></td>
                                        <td class="text-right"><strong>{{ "%.2f"|format(cost_analysis.indirect_cost_total_last) }}</strong></td>
                                        <td class="text-right"><strong>{{ "%.2f"|format(cost_analysis.indirect_cost_change) }}</strong></td>
                                        <td class="text-right"><strong>{{ "%.1f"|format(cost_analysis.indirect_cost_change_rate) }}%</strong></td>
                                    </tr>

                                    <!-- 间接成本明细 -->
                                    {% for item in cost_analysis.indirect_cost_details %}
                                    <tr>
                                        <td>&nbsp;&nbsp;{{ item.name }}</td>
                                        <td class="text-right">{{ "%.2f"|format(item.amount) }}</td>
                                        <td class="text-right">{{ "%.2f"|format(item.amount_last) }}</td>
                                        <td class="text-right">{{ "%.2f"|format(item.change) }}</td>
                                        <td class="text-right">{{ "%.1f"|format(item.change_rate) }}%</td>
                                    </tr>
                                    {% endfor %}

                                    <!-- 总成本 -->
                                    <tr class="table-success">
                                        <td><strong>三、总成本（一+二）</strong></td>
                                        <td class="text-right"><strong>{{ "%.2f"|format(cost_analysis.total_cost) }}</strong></td>
                                        <td class="text-right"><strong>{{ "%.2f"|format(cost_analysis.total_cost_last) }}</strong></td>
                                        <td class="text-right"><strong>{{ "%.2f"|format(cost_analysis.total_cost_change) }}</strong></td>
                                        <td class="text-right"><strong>{{ "%.1f"|format(cost_analysis.total_cost_change_rate) }}%</strong></td>
                                    </tr>

                                    <!-- 成本分析指标 -->
                                    <tr class="table-light">
                                        <td colspan="5"><strong>成本分析指标</strong></td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;&nbsp;单位用餐成本（元/人次）</td>
                                        <td class="text-right">{{ "%.2f"|format(cost_analysis.cost_per_meal) }}</td>
                                        <td class="text-right">{{ "%.2f"|format(cost_analysis.cost_per_meal_last) }}</td>
                                        <td class="text-right">{{ "%.2f"|format(cost_analysis.cost_per_meal_change) }}</td>
                                        <td class="text-right">{{ "%.1f"|format(cost_analysis.cost_per_meal_change_rate) }}%</td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;&nbsp;直接成本占比</td>
                                        <td class="text-right">{{ "%.1f"|format(cost_analysis.direct_cost_ratio) }}%</td>
                                        <td class="text-right">{{ "%.1f"|format(cost_analysis.direct_cost_ratio_last) }}%</td>
                                        <td class="text-right">{{ "%.1f"|format(cost_analysis.direct_cost_ratio_change) }}%</td>
                                        <td class="text-right">-</td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;&nbsp;间接成本占比</td>
                                        <td class="text-right">{{ "%.1f"|format(cost_analysis.indirect_cost_ratio) }}%</td>
                                        <td class="text-right">{{ "%.1f"|format(cost_analysis.indirect_cost_ratio_last) }}%</td>
                                        <td class="text-right">{{ "%.1f"|format(cost_analysis.indirect_cost_ratio_change) }}%</td>
                                        <td class="text-right">-</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 报表说明 -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> 报表说明</h6>
                                <ul class="mb-0">
                                    <li>分析期间：{{ start_date }} 至 {{ end_date }}</li>
                                    <li>编制单位：{{ user_area.name }}</li>
                                    <li>金额单位：人民币元</li>
                                    <li>上期金额为同期对比数据</li>
                                    <li>直接成本：食材采购、加工等直接用于餐饮的成本</li>
                                    <li>间接成本：人工、水电、设备折旧等运营成本</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportReport() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    const url = `{{ url_for('financial.export_report', report_type='cost_analysis') }}?start_date=${startDate}&end_date=${endDate}`;
    window.open(url, '_blank');
}
</script>
{% endblock %}
