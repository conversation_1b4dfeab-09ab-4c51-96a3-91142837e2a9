"""
财务报表路由
"""

from flask import render_template, request, redirect, url_for, flash, jsonify, current_app, make_response
from flask_login import login_required, current_user
from app import db
from app.routes.financial import financial_bp
from app.models_financial import AccountingSubject, FinancialVoucher, VoucherDetail, AccountPayable, PaymentRecord
from app.utils.school_required import school_required
from app.utils.permissions import check_permission
from sqlalchemy import text, func
from datetime import datetime, date, timedelta
import json


class AgingSummary:
    """账龄汇总数据类"""
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)


@financial_bp.route('/reports')
@login_required
@school_required
@check_permission('财务报表', 'view')
def reports_index(user_area):
    """财务报表首页"""
    return render_template('financial/reports/index.html')


@financial_bp.route('/reports/balance-sheet')
@login_required
@school_required
@check_permission('财务报表', 'view')
def balance_sheet(user_area):
    """资产负债表"""

    # 获取查询参数
    report_date = request.args.get('report_date', date.today().strftime('%Y-%m-%d'))

    try:
        report_date_obj = datetime.strptime(report_date, '%Y-%m-%d').date()
    except ValueError:
        report_date_obj = date.today()
        report_date = report_date_obj.strftime('%Y-%m-%d')

    # 查询各类科目
    asset_subjects = AccountingSubject.query.filter_by(
        area_id=user_area.id,
        subject_type='资产',
        is_active=True
    ).order_by(AccountingSubject.code).all()

    liability_subjects = AccountingSubject.query.filter_by(
        area_id=user_area.id,
        subject_type='负债',
        is_active=True
    ).order_by(AccountingSubject.code).all()

    equity_subjects = AccountingSubject.query.filter_by(
        area_id=user_area.id,
        subject_type='所有者权益',
        is_active=True
    ).order_by(AccountingSubject.code).all()

    # 计算各科目余额（简化版本）
    subject_balances = {}

    # 查询应付账款余额
    payable_balance = db.session.query(
        func.sum(AccountPayable.balance_amount)
    ).filter_by(area_id=user_area.id).scalar() or 0

    # 为应付账款科目设置余额
    for subject in liability_subjects:
        if subject.code == '2201':  # 应付账款
            subject_balances[subject.id] = float(payable_balance)
        else:
            subject_balances[subject.id] = 0

    # 其他科目余额设为0（实际应该从凭证明细计算）
    for subject in asset_subjects + equity_subjects:
        subject_balances[subject.id] = 0

    # 构建资产数据结构
    current_assets = []
    non_current_assets = []
    current_assets_total = 0
    non_current_assets_total = 0

    for subject in asset_subjects:
        balance = subject_balances.get(subject.id, 0)
        item = {
            'name': subject.name,
            'amount': balance
        }

        # 根据科目代码判断是流动资产还是非流动资产
        if subject.code.startswith('1001') or subject.code.startswith('1002') or subject.code.startswith('1101'):
            # 流动资产：现金、银行存款、应收账款等
            current_assets.append(item)
            current_assets_total += balance
        else:
            # 非流动资产：固定资产等
            non_current_assets.append(item)
            non_current_assets_total += balance

    total_assets = current_assets_total + non_current_assets_total

    # 构建负债数据结构
    current_liabilities = []
    non_current_liabilities = []
    current_liabilities_total = 0
    non_current_liabilities_total = 0

    for subject in liability_subjects:
        balance = subject_balances.get(subject.id, 0)
        item = {
            'name': subject.name,
            'amount': balance
        }

        # 根据科目代码判断是流动负债还是非流动负债
        if subject.code.startswith('2201') or subject.code.startswith('2202'):
            # 流动负债：应付账款、短期借款等
            current_liabilities.append(item)
            current_liabilities_total += balance
        else:
            # 非流动负债：长期借款等
            non_current_liabilities.append(item)
            non_current_liabilities_total += balance

    total_liabilities = current_liabilities_total + non_current_liabilities_total

    # 构建所有者权益数据结构
    equity_items = []
    total_equity = 0

    for subject in equity_subjects:
        balance = subject_balances.get(subject.id, 0)
        item = {
            'name': subject.name,
            'amount': balance
        }
        equity_items.append(item)
        total_equity += balance

    # 构建最终的数据结构
    assets = {
        'current_assets': current_assets,
        'non_current_assets': non_current_assets,
        'current_assets_total': current_assets_total,
        'non_current_assets_total': non_current_assets_total,
        'total_assets': total_assets
    }

    liabilities = {
        'current_liabilities': current_liabilities,
        'non_current_liabilities': non_current_liabilities,
        'current_liabilities_total': current_liabilities_total,
        'non_current_liabilities_total': non_current_liabilities_total,
        'total_liabilities': total_liabilities
    }

    equity = {
        'equity_items': equity_items,
        'total_equity': total_equity
    }

    return render_template('financial/reports/balance_sheet.html',
                         assets=assets,
                         liabilities=liabilities,
                         equity=equity,
                         subject_balances=subject_balances,
                         report_date=report_date,
                         user_area=user_area)


@financial_bp.route('/reports/cost-analysis')
@login_required
@school_required
@check_permission('财务报表', 'view')
def cost_analysis(user_area):
    """成本分析表"""
    
    # 获取查询参数
    start_date = request.args.get('start_date', date.today().replace(day=1).strftime('%Y-%m-%d'))
    end_date = request.args.get('end_date', date.today().strftime('%Y-%m-%d'))

    try:
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
    except ValueError:
        end_date_obj = date.today()
        start_date_obj = end_date_obj.replace(day=1)
        start_date = start_date_obj.strftime('%Y-%m-%d')
        end_date = end_date_obj.strftime('%Y-%m-%d')

    # 计算上期同期日期
    days_diff = (end_date_obj - start_date_obj).days
    last_end_date = start_date_obj - timedelta(days=1)
    last_start_date = last_end_date - timedelta(days=days_diff)

    # 查询费用科目（成本科目）
    expense_subjects = AccountingSubject.query.filter_by(
        area_id=user_area.id,
        subject_type='费用',
        is_active=True
    ).order_by(AccountingSubject.code).all()

    # 计算本期成本
    current_costs = calculate_period_costs(user_area.id, start_date_obj, end_date_obj, expense_subjects)

    # 计算上期成本
    last_costs = calculate_period_costs(user_area.id, last_start_date, last_end_date, expense_subjects)

    # 构建成本分析数据
    cost_analysis = build_cost_analysis_data(current_costs, last_costs, expense_subjects)

    return render_template('financial/reports/income_statement.html',
                         cost_analysis=cost_analysis,
                         start_date=start_date,
                         end_date=end_date,
                         user_area=user_area)


def calculate_period_costs(area_id, start_date, end_date, expense_subjects):
    """计算期间成本"""
    # 查询期间凭证
    vouchers = FinancialVoucher.query.filter(
        FinancialVoucher.area_id == area_id,
        FinancialVoucher.voucher_date >= start_date,
        FinancialVoucher.voucher_date <= end_date,
        FinancialVoucher.status.in_(['已审核', '已记账'])
    ).all()

    voucher_ids = [v.id for v in vouchers]
    subject_costs = {}

    if voucher_ids:
        # 查询凭证明细
        details = VoucherDetail.query.filter(
            VoucherDetail.voucher_id.in_(voucher_ids)
        ).all()

        # 计算各科目发生额
        for detail in details:
            subject_id = detail.subject_id
            if subject_id not in subject_costs:
                subject_costs[subject_id] = 0

            # 费用科目：借方为成本增加
            subject_costs[subject_id] += float(detail.debit_amount or 0)

    # 为所有费用科目设置默认值
    for subject in expense_subjects:
        if subject.id not in subject_costs:
            subject_costs[subject.id] = 0

    return subject_costs


def build_cost_analysis_data(current_costs, last_costs, expense_subjects):
    """构建成本分析数据结构"""

    # 分类成本科目
    direct_cost_codes = ['5001', '5002', '5003']  # 食材成本、加工成本等
    indirect_cost_codes = ['5101', '5102', '5103', '5201', '5202']  # 人工、水电、折旧等

    direct_cost_details = []
    indirect_cost_details = []
    direct_cost_total = 0
    indirect_cost_total = 0
    direct_cost_total_last = 0
    indirect_cost_total_last = 0

    for subject in expense_subjects:
        current_amount = current_costs.get(subject.id, 0)
        last_amount = last_costs.get(subject.id, 0)
        change = current_amount - last_amount
        change_rate = (change / last_amount * 100) if last_amount > 0 else 0

        cost_item = {
            'name': subject.name,
            'amount': current_amount,
            'amount_last': last_amount,
            'change': change,
            'change_rate': change_rate
        }

        # 根据科目代码分类
        if any(subject.code.startswith(code) for code in direct_cost_codes):
            direct_cost_details.append(cost_item)
            direct_cost_total += current_amount
            direct_cost_total_last += last_amount
        else:
            indirect_cost_details.append(cost_item)
            indirect_cost_total += current_amount
            indirect_cost_total_last += last_amount

    # 计算总成本
    total_cost = direct_cost_total + indirect_cost_total
    total_cost_last = direct_cost_total_last + indirect_cost_total_last

    # 计算变动
    direct_cost_change = direct_cost_total - direct_cost_total_last
    indirect_cost_change = indirect_cost_total - indirect_cost_total_last
    total_cost_change = total_cost - total_cost_last

    # 计算变动率
    direct_cost_change_rate = (direct_cost_change / direct_cost_total_last * 100) if direct_cost_total_last > 0 else 0
    indirect_cost_change_rate = (indirect_cost_change / indirect_cost_total_last * 100) if indirect_cost_total_last > 0 else 0
    total_cost_change_rate = (total_cost_change / total_cost_last * 100) if total_cost_last > 0 else 0

    # 计算成本占比
    direct_cost_ratio = (direct_cost_total / total_cost * 100) if total_cost > 0 else 0
    indirect_cost_ratio = (indirect_cost_total / total_cost * 100) if total_cost > 0 else 0
    direct_cost_ratio_last = (direct_cost_total_last / total_cost_last * 100) if total_cost_last > 0 else 0
    indirect_cost_ratio_last = (indirect_cost_total_last / total_cost_last * 100) if total_cost_last > 0 else 0

    # 假设用餐人次（实际应该从用餐记录获取）
    meal_count = 1000  # 简化处理
    meal_count_last = 1000

    cost_per_meal = total_cost / meal_count if meal_count > 0 else 0
    cost_per_meal_last = total_cost_last / meal_count_last if meal_count_last > 0 else 0
    cost_per_meal_change = cost_per_meal - cost_per_meal_last
    cost_per_meal_change_rate = (cost_per_meal_change / cost_per_meal_last * 100) if cost_per_meal_last > 0 else 0

    return {
        'direct_cost_details': direct_cost_details,
        'indirect_cost_details': indirect_cost_details,
        'direct_cost_total': direct_cost_total,
        'direct_cost_total_last': direct_cost_total_last,
        'direct_cost_change': direct_cost_change,
        'direct_cost_change_rate': direct_cost_change_rate,
        'indirect_cost_total': indirect_cost_total,
        'indirect_cost_total_last': indirect_cost_total_last,
        'indirect_cost_change': indirect_cost_change,
        'indirect_cost_change_rate': indirect_cost_change_rate,
        'total_cost': total_cost,
        'total_cost_last': total_cost_last,
        'total_cost_change': total_cost_change,
        'total_cost_change_rate': total_cost_change_rate,
        'cost_per_meal': cost_per_meal,
        'cost_per_meal_last': cost_per_meal_last,
        'cost_per_meal_change': cost_per_meal_change,
        'cost_per_meal_change_rate': cost_per_meal_change_rate,
        'direct_cost_ratio': direct_cost_ratio,
        'indirect_cost_ratio': indirect_cost_ratio,
        'direct_cost_ratio_last': direct_cost_ratio_last,
        'indirect_cost_ratio_last': indirect_cost_ratio_last,
        'direct_cost_ratio_change': direct_cost_ratio - direct_cost_ratio_last,
        'indirect_cost_ratio_change': indirect_cost_ratio - indirect_cost_ratio_last
    }


@financial_bp.route('/reports/payables-aging')
@login_required
@school_required
@check_permission('财务报表', 'view')
def payables_aging(user_area):
    """应付账款账龄分析"""

    # 获取查询参数
    analysis_date = request.args.get('analysis_date', date.today().strftime('%Y-%m-%d'))
    supplier_id = request.args.get('supplier_id', '')

    try:
        analysis_date_obj = datetime.strptime(analysis_date, '%Y-%m-%d').date()
    except ValueError:
        analysis_date_obj = date.today()
        analysis_date = analysis_date_obj.strftime('%Y-%m-%d')

    # 获取供应商列表用于筛选
    from app.models import Supplier
    suppliers = Supplier.query.filter_by(area_id=user_area.id).order_by(Supplier.name).all()

    # 获取所有未付清的应付账款，预加载供应商信息
    query = AccountPayable.query.options(
        db.joinedload(AccountPayable.supplier)
    ).filter(
        AccountPayable.area_id == user_area.id,
        AccountPayable.balance_amount > 0
    )

    # 如果指定了供应商，添加筛选条件
    if supplier_id:
        try:
            supplier_id_int = int(supplier_id)
            query = query.filter(AccountPayable.supplier_id == supplier_id_int)
        except ValueError:
            pass  # 忽略无效的供应商ID

    payables = query.order_by(AccountPayable.created_at.desc()).all()

    # 计算账龄
    today = analysis_date_obj
    aging_data = []
    payables_detail = []

    # 初始化统计数据
    aging_stats = {
        'within_30': {'count': 0, 'amount': 0, 'suppliers': set()},
        'days_31_60': {'count': 0, 'amount': 0, 'suppliers': set()},
        'days_61_90': {'count': 0, 'amount': 0, 'suppliers': set()},
        'days_91_180': {'count': 0, 'amount': 0, 'suppliers': set()},
        'over_180': {'count': 0, 'amount': 0, 'suppliers': set()}
    }

    for payable in payables:
        created_date = payable.created_at.date() if payable.created_at else today
        days_outstanding = (today - created_date).days
        balance_amount = float(payable.balance_amount)

        # 分类账龄
        if days_outstanding <= 30:
            aging_category = '30天以内'
            aging_key = 'within_30'
        elif days_outstanding <= 60:
            aging_category = '31-60天'
            aging_key = 'days_31_60'
        elif days_outstanding <= 90:
            aging_category = '61-90天'
            aging_key = 'days_61_90'
        elif days_outstanding <= 180:
            aging_category = '91-180天'
            aging_key = 'days_91_180'
        else:
            aging_category = '180天以上'
            aging_key = 'over_180'

        # 获取供应商名称
        supplier_name = payable.supplier.name if payable.supplier else '未知供应商'

        # 更新统计数据
        aging_stats[aging_key]['count'] += 1
        aging_stats[aging_key]['amount'] += balance_amount
        aging_stats[aging_key]['suppliers'].add(supplier_name)

        aging_data.append({
            'payable': payable,
            'days_outstanding': days_outstanding,
            'aging_category': aging_category
        })

        # 构建供应商明细数据
        payables_detail.append({
            'supplier_name': supplier_name,
            'payable_number': payable.payable_number,
            'original_amount': float(payable.original_amount),
            'balance_amount': balance_amount,
            'aging_days': days_outstanding,
            'created_at': payable.created_at.strftime('%Y-%m-%d') if payable.created_at else '未知'
        })

    # 计算总金额和百分比
    total_amount = sum(stat['amount'] for stat in aging_stats.values())
    total_suppliers = len(set().union(*[stat['suppliers'] for stat in aging_stats.values()]))

    # 构建模板期望的数据结构
    aging_summary = AgingSummary(
        within_30=aging_stats['within_30']['amount'],
        within_30_percent=(aging_stats['within_30']['amount'] / total_amount * 100) if total_amount > 0 else 0,
        within_30_suppliers=len(aging_stats['within_30']['suppliers']),

        days_31_60=aging_stats['days_31_60']['amount'],
        days_31_60_percent=(aging_stats['days_31_60']['amount'] / total_amount * 100) if total_amount > 0 else 0,
        days_31_60_suppliers=len(aging_stats['days_31_60']['suppliers']),

        days_61_90=aging_stats['days_61_90']['amount'],
        days_61_90_percent=(aging_stats['days_61_90']['amount'] / total_amount * 100) if total_amount > 0 else 0,
        days_61_90_suppliers=len(aging_stats['days_61_90']['suppliers']),

        days_91_180=aging_stats['days_91_180']['amount'],
        days_91_180_percent=(aging_stats['days_91_180']['amount'] / total_amount * 100) if total_amount > 0 else 0,
        days_91_180_suppliers=len(aging_stats['days_91_180']['suppliers']),

        over_180=aging_stats['over_180']['amount'],
        over_180_percent=(aging_stats['over_180']['amount'] / total_amount * 100) if total_amount > 0 else 0,
        over_180_suppliers=len(aging_stats['over_180']['suppliers']),

        total_amount=total_amount,
        total_suppliers=total_suppliers
    )
    
    return render_template('financial/reports/payables_aging.html',
                         aging_data=aging_data,
                         aging_summary=aging_summary,
                         payables_detail=payables_detail,
                         suppliers=suppliers,
                         supplier_id=int(supplier_id) if supplier_id else '',
                         analysis_date=analysis_date,
                         user_area=user_area)


@financial_bp.route('/reports/voucher-summary')
@login_required
@school_required
@check_permission('财务报表', 'view')
def voucher_summary(user_area):
    """凭证汇总表"""

    # 获取查询参数
    start_date = request.args.get('start_date', date.today().replace(day=1).strftime('%Y-%m-%d'))
    end_date = request.args.get('end_date', date.today().strftime('%Y-%m-%d'))
    voucher_type = request.args.get('voucher_type', '')
    
    try:
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
    except ValueError:
        end_date_obj = date.today()
        start_date_obj = end_date_obj.replace(day=1)
        start_date = start_date_obj.strftime('%Y-%m-%d')
        end_date = end_date_obj.strftime('%Y-%m-%d')
    
    # 查询期间凭证统计
    query = db.session.query(
        FinancialVoucher.voucher_type,
        FinancialVoucher.status,
        func.count(FinancialVoucher.id).label('count'),
        func.sum(FinancialVoucher.total_amount).label('total_amount')
    ).filter(
        FinancialVoucher.area_id == user_area.id,
        FinancialVoucher.voucher_date >= start_date_obj,
        FinancialVoucher.voucher_date <= end_date_obj
    )

    # 如果指定了凭证类型，添加过滤条件
    if voucher_type:
        query = query.filter(FinancialVoucher.voucher_type == voucher_type)

    voucher_stats = query.group_by(
        FinancialVoucher.voucher_type,
        FinancialVoucher.status
    ).all()
    
    # 整理统计数据
    summary_data = {}
    for voucher_type, status, count, total_amount in voucher_stats:
        if voucher_type not in summary_data:
            summary_data[voucher_type] = {}
        
        summary_data[voucher_type][status] = {
            'count': count,
            'total_amount': float(total_amount) if total_amount else 0
        }
    
    return render_template('financial/reports/voucher_summary.html',
                         summary_data=summary_data,
                         start_date=start_date,
                         end_date=end_date,
                         voucher_type=voucher_type,
                         user_area=user_area)


@financial_bp.route('/reports/export/<report_type>')
@login_required
@school_required
@check_permission('财务报表', 'export')
def export_report(report_type, user_area):
    """导出报表"""
    # 这里可以实现Excel导出功能
    # 暂时返回JSON格式
    
    if report_type == 'payables':
        # 导出应付账款
        payables = AccountPayable.query.filter_by(area_id=user_area.id).all()
        data = [payable.to_dict() for payable in payables]
    elif report_type == 'payables_aging':
        # 导出应付账款账龄分析
        analysis_date = request.args.get('analysis_date', date.today().strftime('%Y-%m-%d'))

        try:
            analysis_date_obj = datetime.strptime(analysis_date, '%Y-%m-%d').date()
        except ValueError:
            analysis_date_obj = date.today()

        # 获取应付账款数据
        payables = AccountPayable.query.options(
            db.joinedload(AccountPayable.supplier)
        ).filter(
            AccountPayable.area_id == user_area.id,
            AccountPayable.balance_amount > 0
        ).all()

        # 构建导出数据
        export_data = []
        for payable in payables:
            created_date = payable.created_at.date() if payable.created_at else analysis_date_obj
            days_outstanding = (analysis_date_obj - created_date).days
            supplier_name = payable.supplier.name if payable.supplier else '未知供应商'

            export_data.append({
                'supplier_name': supplier_name,
                'payable_number': payable.payable_number,
                'original_amount': float(payable.original_amount),
                'balance_amount': float(payable.balance_amount),
                'aging_days': days_outstanding,
                'created_date': created_date.strftime('%Y-%m-%d'),
                'analysis_date': analysis_date
            })

        data = {
            'report_type': '应付账款账龄分析',
            'analysis_date': analysis_date,
            'school': user_area.name,
            'payables_detail': export_data
        }
    elif report_type == 'detail_ledger':
        # 导出明细账
        subject_id = request.args.get('subject_id', type=int)
        start_date = request.args.get('start_date', date.today().replace(day=1).strftime('%Y-%m-%d'))
        end_date = request.args.get('end_date', date.today().strftime('%Y-%m-%d'))

        if not subject_id:
            return jsonify({'error': '请指定科目'}), 400

        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'error': '日期格式错误'}), 400

        # 导入明细账查询函数
        from app.routes.financial.ledgers import get_subject_detail_records, calculate_opening_balance
        from app.models_financial import AccountingSubject

        # 获取科目信息
        subject = AccountingSubject.query.filter_by(
            id=subject_id,
            area_id=user_area.id
        ).first()

        if not subject:
            return jsonify({'error': '科目不存在'}), 404

        # 获取明细记录
        detail_records = get_subject_detail_records(user_area.id, subject_id, start_date_obj, end_date_obj)
        opening_balance = calculate_opening_balance(user_area.id, subject_id, start_date_obj)

        data = {
            'report_type': '明细账',
            'subject_code': subject.code,
            'subject_name': subject.name,
            'start_date': start_date,
            'end_date': end_date,
            'opening_balance': opening_balance,
            'school': user_area.name,
            'detail_records': detail_records
        }
    elif report_type == 'general_ledger':
        # 导出总账
        start_date = request.args.get('start_date', date.today().replace(day=1).strftime('%Y-%m-%d'))
        end_date = request.args.get('end_date', date.today().strftime('%Y-%m-%d'))
        subject_type = request.args.get('subject_type', '')

        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'error': '日期格式错误'}), 400

        # 导入总账查询函数
        from app.routes.financial.ledgers import get_general_ledger_summary

        # 获取总账数据
        general_ledger_data = get_general_ledger_summary(user_area.id, start_date_obj, end_date_obj, subject_type)

        data = {
            'report_type': '总账',
            'start_date': start_date,
            'end_date': end_date,
            'subject_type': subject_type or '全部',
            'school': user_area.name,
            'general_ledger_data': general_ledger_data
        }
    elif report_type == 'balance_detail':
        # 导出科目余额表
        balance_date = request.args.get('balance_date', date.today().strftime('%Y-%m-%d'))
        subject_type = request.args.get('subject_type', '')

        try:
            balance_date_obj = datetime.strptime(balance_date, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'error': '日期格式错误'}), 400

        # 导入科目余额查询函数
        from app.routes.financial.ledgers import get_subject_balances

        # 获取科目余额数据
        balance_data = get_subject_balances(user_area.id, balance_date_obj, subject_type)

        data = {
            'report_type': '科目余额表',
            'balance_date': balance_date,
            'subject_type': subject_type or '全部',
            'school': user_area.name,
            'balance_data': balance_data
        }
    elif report_type == 'payments':
        # 导出付款记录
        payments = PaymentRecord.query.filter_by(area_id=user_area.id).all()
        data = [payment.to_dict() for payment in payments]
    elif report_type == 'cost_analysis':
        # 导出成本分析表
        # 获取查询参数
        start_date = request.args.get('start_date', date.today().replace(day=1).strftime('%Y-%m-%d'))
        end_date = request.args.get('end_date', date.today().strftime('%Y-%m-%d'))

        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
        except ValueError:
            end_date_obj = date.today()
            start_date_obj = end_date_obj.replace(day=1)

        # 计算上期同期日期
        days_diff = (end_date_obj - start_date_obj).days
        last_end_date = start_date_obj - timedelta(days=1)
        last_start_date = last_end_date - timedelta(days=days_diff)

        # 查询费用科目
        expense_subjects = AccountingSubject.query.filter_by(
            area_id=user_area.id,
            subject_type='费用',
            is_active=True
        ).order_by(AccountingSubject.code).all()

        # 计算成本数据
        current_costs = calculate_period_costs(user_area.id, start_date_obj, end_date_obj, expense_subjects)
        last_costs = calculate_period_costs(user_area.id, last_start_date, last_end_date, expense_subjects)
        cost_analysis = build_cost_analysis_data(current_costs, last_costs, expense_subjects)

        data = {
            'report_type': '成本分析表',
            'period': f'{start_date} 至 {end_date}',
            'school': user_area.name,
            'cost_analysis': cost_analysis
        }
    else:
        data = []
    
    response = make_response(json.dumps(data, ensure_ascii=False, indent=2))
    response.headers['Content-Type'] = 'application/json; charset=utf-8'
    response.headers['Content-Disposition'] = f'attachment; filename={report_type}_report.json'
    
    return response
