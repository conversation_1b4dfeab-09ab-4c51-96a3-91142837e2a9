/**
 * 首页交互脚本
 * 处理导航、图表、轮播图等功能
 */

(function() {
    'use strict';
    
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        initNavigation();
        initCarousel();
        initContactForm();
        initScrollEffects();
        initVideoTutorials();
    });
    
    // 导航功能
    function initNavigation() {
        const menuToggle = document.getElementById('menu-toggle');
        const mobileMenu = document.getElementById('mobile-menu');
        
        if (menuToggle && mobileMenu) {
            menuToggle.addEventListener('click', function() {
                mobileMenu.classList.toggle('hidden');
            });
        }
        
        // 平滑滚动
        const navLinks = document.querySelectorAll('a[href^="#"]');
        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                    
                    // 关闭移动端菜单
                    if (mobileMenu) {
                        mobileMenu.classList.add('hidden');
                    }
                }
            });
        });
    }
    

    
    // 初始化轮播图
    function initCarousel() {
        if (window.CarouselDataLoader) {
            window.CarouselDataLoader.initHeroCarousel().catch(error => {
                console.error('轮播图初始化失败:', error);
            });
        }
    }
    
    // 初始化联系表单
    function initContactForm() {
        const form = document.getElementById('consultationForm');
        if (!form) return;
        
        form.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn ? submitBtn.textContent : '';
            
            try {
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.textContent = '提交中...';
                }
                
                const formData = new FormData(form);
                const data = {
                    name: formData.get('name'),
                    contact_type: formData.get('contact_type'),
                    contact_value: formData.get('contact_value'),
                    content: formData.get('content')
                };
                
                const response = await fetch('/consultation/api/submit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': formData.get('csrf_token')
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showMessage('咨询提交成功！我们会尽快与您联系。', 'success');
                    form.reset();
                } else {
                    showMessage(result.message || '提交失败，请重试', 'error');
                }
                
            } catch (error) {
                console.error('表单提交错误:', error);
                showMessage('网络错误，请检查网络连接后重试', 'error');
            } finally {
                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.textContent = originalText;
                }
            }
        });
    }
    
    // 显示消息
    function showMessage(message, type = 'info') {
        const messageEl = document.createElement('div');
        messageEl.className = `message message-${type}`;
        messageEl.textContent = message;
        
        document.body.appendChild(messageEl);
        
        // 显示动画
        setTimeout(() => {
            messageEl.classList.add('show');
        }, 100);
        
        // 自动隐藏
        setTimeout(() => {
            messageEl.classList.remove('show');
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.parentNode.removeChild(messageEl);
                }
            }, 300);
        }, 3000);
    }
    
    // 滚动效果
    function initScrollEffects() {
        // 导航栏滚动效果
        const navbar = document.getElementById('navbar');
        if (navbar) {
            window.addEventListener('scroll', function() {
                if (window.scrollY > 100) {
                    navbar.style.background = 'rgba(11, 14, 47, 0.95)';
                } else {
                    navbar.style.background = 'rgba(11, 14, 47, 0.8)';
                }
            });
        }
        
        // 元素进入视口动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);
        
        // 观察需要动画的元素
        const animateElements = document.querySelectorAll('.card-hover, .group');
        animateElements.forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });
    }
    
    // 功能分组切换
    window.switchFeatureGroup = function(groupName) {
        const buttons = document.querySelectorAll('[data-group]');
        const cards = document.querySelectorAll('[data-feature-group]');
        
        // 更新按钮状态
        buttons.forEach(btn => {
            if (btn.dataset.group === groupName) {
                btn.style.background = 'rgba(22, 93, 255, 0.2)';
                btn.style.color = 'white';
            } else {
                btn.style.background = 'rgba(22, 93, 255, 0.1)';
                btn.style.color = '#9CA3AF';
            }
        });
        
        // 显示/隐藏功能卡片
        cards.forEach(card => {
            if (card.dataset.featureGroup === groupName || groupName === 'all') {
                card.style.display = 'block';
                card.style.opacity = '1';
            } else {
                card.style.opacity = '0.3';
            }
        });
    };
    
    // 视频播放
    window.playDemoVideo = function() {
        showMessage('功能演示视频即将上线，敬请期待！', 'info');
    };
    
    // 视频教程功能
    function initVideoTutorials() {
        const tutorialCards = document.querySelectorAll('.tutorial-card');
        const modal = document.getElementById('videoModal');
        const closeButton = document.querySelector('.close-button');
        const videoElement = document.querySelector('#videoModal video');
        const videoTitle = document.querySelector('#videoModal .video-info h3');
        const videoDesc = document.querySelector('#videoModal .video-info p');

        // 点击教程卡片打开视频
        tutorialCards.forEach(card => {
            card.addEventListener('click', () => {
                const videoSrc = card.dataset.video;
                const title = card.querySelector('h3').textContent;
                const description = card.querySelector('p').textContent;

                videoElement.src = videoSrc;
                videoTitle.textContent = title;
                videoDesc.textContent = description;
                modal.style.display = 'block';
                videoElement.play();
            });
        });

        // 关闭视频模态框
        closeButton.addEventListener('click', () => {
            modal.style.display = 'none';
            videoElement.pause();
            videoElement.src = '';
        });

        // 点击模态框背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.style.display = 'none';
                videoElement.pause();
                videoElement.src = '';
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal.style.display === 'block') {
                modal.style.display = 'none';
                videoElement.pause();
                videoElement.src = '';
            }
        });
    }
    
})();
