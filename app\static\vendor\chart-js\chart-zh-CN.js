/**
 * Chart.js 中文本地化
 */
(function() {
    'use strict';
    
    if (typeof Chart !== 'undefined') {
        // 设置全局默认值
        Chart.defaults.global.defaultFontFamily = "'Microsoft YaHei', 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif";
        
        // 本地化工具提示和图例
        Chart.defaults.global.tooltips.callbacks.title = function(tooltipItems, data) {
            var title = data.datasets[tooltipItems[0].datasetIndex].label || '';
            return title;
        };
        
        Chart.defaults.global.tooltips.callbacks.label = function(tooltipItem, data) {
            var datasetLabel = data.datasets[tooltipItem.datasetIndex].label || '';
            var value = tooltipItem.yLabel;
            
            // 格式化数字，添加千位分隔符
            if (typeof value === 'number') {
                value = value.toLocaleString('zh-CN');
            }
            
            return datasetLabel + ': ' + value;
        };
        
        // 本地化时间轴
        if (typeof Chart.scaleService !== 'undefined' && Chart.scaleService.getScaleConstructor('time')) {
            var timeScale = Chart.scaleService.getScaleConstructor('time');
            var originalBuildTicks = timeScale.prototype.buildTicks;
            
            timeScale.prototype.buildTicks = function() {
                var ticks = originalBuildTicks.call(this);
                
                // 本地化时间显示
                for (var i = 0; i < ticks.length; i++) {
                    var tick = ticks[i];
                    var date = new Date(tick.value);
                    
                    // 根据时间单位格式化日期
                    if (this.options.time && this.options.time.unit) {
                        switch (this.options.time.unit) {
                            case 'day':
                                tick.label = date.getFullYear() + '年' + (date.getMonth() + 1) + '月' + date.getDate() + '日';
                                break;
                            case 'month':
                                tick.label = date.getFullYear() + '年' + (date.getMonth() + 1) + '月';
                                break;
                            case 'year':
                                tick.label = date.getFullYear() + '年';
                                break;
                        }
                    }
                }
                
                return ticks;
            };
        }
        
        // 本地化图例
        var originalGenerateLabels = Chart.defaults.global.legend.labels.generateLabels;
        Chart.defaults.global.legend.labels.generateLabels = function(chart) {
            var labels = originalGenerateLabels.call(this, chart);
            
            // 本地化图例标签
            for (var i = 0; i < labels.length; i++) {
                var label = labels[i];
                
                // 这里可以添加自定义的本地化逻辑
                // 例如，将特定的英文标签翻译成中文
                if (label.text === 'Revenue') {
                    label.text = '收入';
                } else if (label.text === 'Expenses') {
                    label.text = '支出';
                } else if (label.text === 'Profit') {
                    label.text = '利润';
                }
            }
            
            return labels;
        };
        
        // 添加中文格式化方法
        Chart.ChineseFormat = {
            // 格式化数字为中文货币格式
            currency: function(value) {
                return '¥' + value.toLocaleString('zh-CN', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                });
            },
            
            // 格式化数字为中文百分比格式
            percent: function(value) {
                return (value * 100).toLocaleString('zh-CN', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                }) + '%';
            },
            
            // 格式化数字为中文数字格式（带千位分隔符）
            number: function(value) {
                return value.toLocaleString('zh-CN');
            },
            
            // 格式化日期为中文日期格式
            date: function(value) {
                var date = new Date(value);
                return date.getFullYear() + '年' + (date.getMonth() + 1) + '月' + date.getDate() + '日';
            }
        };
    }
})();
