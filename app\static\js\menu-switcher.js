/**
 * 菜单切换器 - 在原有菜单和优雅菜单之间切换
 * 影响最小的导航菜单优化方案
 */

class MenuSwitcher {
    constructor() {
        this.currentMode = localStorage.getItem('menu_mode') || 'elegant';
        this.init();
    }

    init() {
        this.createSwitchButton();
        this.applyMenuMode();
        this.bindEvents();
    }

    /**
     * 创建菜单切换按钮
     */
    createSwitchButton() {
        // 检查是否已存在切换按钮
        if (document.getElementById('menuSwitcher')) {
            return;
        }

        // 创建切换按钮
        const switchButton = document.createElement('li');
        switchButton.className = 'nav-item dropdown';
        switchButton.innerHTML = `
            <a class="nav-link" href="#" id="menuSwitcher" role="button" data-toggle="dropdown" 
               aria-haspopup="true" aria-expanded="false" title="菜单模式">
                <i class="fas fa-bars"></i>
            </a>
            <div class="dropdown-menu dropdown-menu-right" aria-labelledby="menuSwitcher">
                <h6 class="dropdown-header">菜单模式</h6>
                <a class="dropdown-item menu-mode-option" href="#" data-mode="elegant">
                    <i class="fas fa-magic"></i> 优雅模式
                    <small class="text-muted d-block">简洁分组，5个主菜单</small>
                </a>
                <a class="dropdown-item menu-mode-option" href="#" data-mode="classic">
                    <i class="fas fa-list"></i> 经典模式
                    <small class="text-muted d-block">完整展示，11个主菜单</small>
                </a>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item" href="#" id="menuPreview">
                    <i class="fas fa-eye"></i> 预览效果
                </a>
            </div>
        `;

        // 插入到主题切换器之前
        const themeDropdown = document.getElementById('themeDropdown');
        if (themeDropdown) {
            themeDropdown.closest('li').parentNode.insertBefore(switchButton, themeDropdown.closest('li'));
        } else {
            // 如果没有主题切换器，插入到导航栏右侧
            const navbarNav = document.querySelector('.navbar-nav:last-child');
            if (navbarNav) {
                navbarNav.insertBefore(switchButton, navbarNav.firstChild);
            }
        }
    }

    /**
     * 应用菜单模式
     */
    applyMenuMode() {
        const body = document.body;
        
        if (this.currentMode === 'elegant') {
            body.classList.add('elegant-menu');
            body.classList.remove('classic-menu');
            this.loadElegantMenu();
        } else {
            body.classList.add('classic-menu');
            body.classList.remove('elegant-menu');
            this.loadClassicMenu();
        }

        // 更新切换按钮状态
        this.updateSwitchButton();
    }

    /**
     * 加载优雅菜单
     */
    loadElegantMenu() {
        // 发送请求获取优雅菜单数据
        fetch('/api/menu/elegant', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.renderMenu(data.menu, 'elegant');
            }
        })
        .catch(error => {
            console.warn('加载优雅菜单失败，使用默认菜单:', error);
        });
    }

    /**
     * 加载经典菜单
     */
    loadClassicMenu() {
        // 发送请求获取经典菜单数据
        fetch('/api/menu/classic', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.renderMenu(data.menu, 'classic');
            }
        })
        .catch(error => {
            console.warn('加载经典菜单失败，使用默认菜单:', error);
        });
    }

    /**
     * 渲染菜单
     */
    renderMenu(menuData, mode) {
        const navbarNav = document.querySelector('.navbar-nav:first-child');
        if (!navbarNav) return;

        // 清空现有菜单（保留用户相关的菜单项）
        const userMenuItems = navbarNav.querySelectorAll('.nav-item:not([data-menu-item])');
        navbarNav.innerHTML = '';
        
        // 重新添加用户菜单项
        userMenuItems.forEach(item => {
            navbarNav.appendChild(item);
        });

        // 渲染新菜单
        menuData.forEach(item => {
            const menuItem = this.createMenuItem(item, mode);
            navbarNav.appendChild(menuItem);
        });

        // 应用样式
        this.applyMenuStyles(mode);
    }

    /**
     * 创建菜单项
     */
    createMenuItem(item, mode) {
        const li = document.createElement('li');
        li.className = 'nav-item';
        li.setAttribute('data-menu-item', 'true');

        if (item.children && item.children.length > 0) {
            // 有子菜单的情况
            li.classList.add('dropdown');
            
            const link = document.createElement('a');
            link.className = 'nav-link dropdown-toggle';
            link.href = item.url || '#';
            link.setAttribute('role', 'button');
            link.setAttribute('data-toggle', 'dropdown');
            link.innerHTML = `${item.icon ? `<i class="${item.icon}"></i> ` : ''}${item.name}`;
            
            const dropdown = document.createElement('div');
            dropdown.className = 'dropdown-menu';
            
            item.children.forEach(child => {
                if (child.is_header) {
                    // 分组标题
                    const header = document.createElement('h6');
                    header.className = 'dropdown-header';
                    header.textContent = child.name;
                    dropdown.appendChild(header);
                } else {
                    // 普通菜单项
                    const childLink = document.createElement('a');
                    childLink.className = 'dropdown-item';
                    childLink.href = child.url || '#';
                    childLink.innerHTML = `${child.icon ? `<i class="${child.icon}"></i> ` : ''}${child.name}`;
                    dropdown.appendChild(childLink);
                }
            });
            
            li.appendChild(link);
            li.appendChild(dropdown);
        } else {
            // 没有子菜单的情况
            const link = document.createElement('a');
            link.className = 'nav-link';
            link.href = item.url || '#';
            link.innerHTML = `${item.icon ? `<i class="${item.icon}"></i> ` : ''}${item.name}`;
            li.appendChild(link);
        }

        return li;
    }

    /**
     * 应用菜单样式
     */
    applyMenuStyles(mode) {
        // 移除旧样式
        const oldStyleLink = document.getElementById('menu-mode-styles');
        if (oldStyleLink) {
            oldStyleLink.remove();
        }

        // 添加新样式
        if (mode === 'elegant') {
            const styleLink = document.createElement('link');
            styleLink.id = 'menu-mode-styles';
            styleLink.rel = 'stylesheet';
            styleLink.href = '/static/css/elegant-navigation.css?v=' + Date.now();
            document.head.appendChild(styleLink);
        }
    }

    /**
     * 更新切换按钮状态
     */
    updateSwitchButton() {
        const options = document.querySelectorAll('.menu-mode-option');
        options.forEach(option => {
            const mode = option.getAttribute('data-mode');
            if (mode === this.currentMode) {
                option.classList.add('active');
                option.style.background = 'var(--theme-primary)';
                option.style.color = 'white';
            } else {
                option.classList.remove('active');
                option.style.background = '';
                option.style.color = '';
            }
        });
    }

    /**
     * 切换菜单模式
     */
    switchMode(mode) {
        if (mode === this.currentMode) return;

        this.currentMode = mode;
        localStorage.setItem('menu_mode', mode);
        
        // 显示切换动画
        this.showSwitchAnimation();
        
        // 延迟应用新模式，让动画效果更好
        setTimeout(() => {
            this.applyMenuMode();
        }, 300);
    }

    /**
     * 显示切换动画
     */
    showSwitchAnimation() {
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            navbar.style.transition = 'all 0.3s ease';
            navbar.style.opacity = '0.7';
            navbar.style.transform = 'translateY(-5px)';
            
            setTimeout(() => {
                navbar.style.opacity = '1';
                navbar.style.transform = 'translateY(0)';
            }, 300);
        }
    }

    /**
     * 预览菜单效果
     */
    previewMenu() {
        const previewMode = this.currentMode === 'elegant' ? 'classic' : 'elegant';
        
        // 创建预览提示
        const toast = document.createElement('div');
        toast.className = 'toast';
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--theme-primary);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 9999;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        `;
        toast.innerHTML = `
            <i class="fas fa-eye"></i> 
            预览${previewMode === 'elegant' ? '优雅' : '经典'}模式 
            <button onclick="this.parentElement.remove()" style="background:none;border:none;color:white;margin-left:10px;">×</button>
        `;
        
        document.body.appendChild(toast);
        
        // 临时切换模式
        const originalMode = this.currentMode;
        this.switchMode(previewMode);
        
        // 5秒后恢复原模式
        setTimeout(() => {
            this.switchMode(originalMode);
            toast.remove();
        }, 5000);
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 菜单模式切换
        document.addEventListener('click', (e) => {
            if (e.target.closest('.menu-mode-option')) {
                e.preventDefault();
                const mode = e.target.closest('.menu-mode-option').getAttribute('data-mode');
                this.switchMode(mode);
            }
        });

        // 预览功能
        document.addEventListener('click', (e) => {
            if (e.target.closest('#menuPreview')) {
                e.preventDefault();
                this.previewMenu();
            }
        });

        // 键盘快捷键 Ctrl+M 切换菜单模式
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'm') {
                e.preventDefault();
                const newMode = this.currentMode === 'elegant' ? 'classic' : 'elegant';
                this.switchMode(newMode);
            }
        });
    }
}

// 页面加载完成后初始化菜单切换器 - 已禁用
// document.addEventListener('DOMContentLoaded', function() {
//     // 延迟初始化，确保页面完全加载
//     setTimeout(() => {
//         new MenuSwitcher();
//     }, 100);
// });

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MenuSwitcher;
}
