<!DOCTYPE html>
<html lang="zh-CN" data-theme="yonyou">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用友风格统一凭证界面测试</title>
    <link rel="stylesheet" href="app/static/bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="app/static/css/theme-colors.css">
    <link rel="stylesheet" href="app/static/financial/css/yonyou-theme.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        /* 用友风格专业凭证编辑器样式 */
        .voucher-container {
            background: #f5f7fa;
            min-height: 100vh;
            padding: 10px;
        }

        .voucher-window {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(30, 136, 229, 0.1);
            margin: 0 auto;
            max-width: 1200px;
        }

        .voucher-header {
            background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
            border-bottom: 1px solid #90caf9;
            padding: 8px 15px;
            font-size: 14px;
            font-weight: bold;
            color: #1565c0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .voucher-toolbar {
            background: #f8f8f8;
            border-bottom: 1px solid #e0e0e0;
            padding: 5px 10px;
            display: flex;
            gap: 5px;
            align-items: center;
        }

        .toolbar-btn {
            background: linear-gradient(to bottom, #ffffff, #f5f5f5);
            border: 1px solid #e0e0e0;
            border-radius: 3px;
            padding: 4px 8px;
            font-size: 12px;
            color: #333;
            cursor: pointer;
            min-width: 60px;
            text-align: center;
        }

        .toolbar-btn:hover {
            background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
            color: #1565c0;
        }

        .voucher-info-bar {
            background: #f5f5f5;
            border-bottom: 1px solid #e0e0e0;
            padding: 8px 15px;
            display: grid;
            grid-template-columns: auto auto auto auto 1fr auto;
            gap: 20px;
            align-items: center;
            font-size: 12px;
        }

        .info-group {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .info-label {
            color: #666;
            font-weight: normal;
        }

        .info-input {
            border: 1px solid #e0e0e0;
            padding: 2px 5px;
            font-size: 12px;
            background: white;
            border-radius: 3px;
        }

        .voucher-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
            margin: 0;
        }

        .voucher-table th {
            background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
            border: 1px solid #90caf9;
            padding: 6px 4px;
            text-align: center;
            font-weight: normal;
            color: #1565c0;
            font-size: 12px;
        }

        .voucher-table td {
            border: 1px solid #e0e0e0;
            padding: 2px;
            vertical-align: middle;
            background: white;
        }

        .cell-input {
            border: none;
            background: transparent;
            width: 100%;
            padding: 4px;
            font-size: 12px;
            outline: none;
            font-family: inherit;
        }

        .signature-area {
            background: #f8f8f8;
            border-top: 1px solid #e0e0e0;
            padding: 10px 15px;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            font-size: 12px;
        }

        .signature-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .signature-label {
            color: #666;
            min-width: 40px;
        }

        .signature-box {
            border-bottom: 1px solid #999;
            min-width: 80px;
            height: 20px;
            display: inline-block;
        }

        .voucher-status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: normal;
            margin-left: 10px;
        }

        .voucher-status.approved {
            background-color: #e8f5e9;
            color: #2e7d32;
        }
    </style>
</head>
<body>
    <div class="voucher-container mode-view">
        <div class="voucher-window">
            <!-- 窗口标题栏 -->
            <div class="voucher-header">
                <span>记账凭证
                    <span class="voucher-status approved">已审核</span>
                </span>
                <div class="window-controls">
                    <button class="toolbar-btn">✏️ 编辑</button>
                    <button class="toolbar-btn">📋 列表</button>
                    <button class="toolbar-btn">✕ 关闭</button>
                </div>
            </div>

            <!-- 工具栏 -->
            <div class="voucher-toolbar">
                <button class="toolbar-btn">⚖️ 平衡</button>
                <button class="toolbar-btn">🖨️ 打印</button>
                <button class="toolbar-btn">📤 导出</button>
                <div style="margin-left: auto;">
                    <span style="padding: 2px 8px; border-radius: 3px; font-size: 11px; font-weight: bold; background: #e8f5e9; color: #2e7d32;">借贷平衡</span>
                </div>
            </div>

            <!-- 凭证信息栏 -->
            <div class="voucher-info-bar">
                <div class="info-group">
                    <span class="info-label">凭证字:</span>
                    <select class="info-input" style="width: 80px;" disabled>
                        <option selected>记</option>
                    </select>
                </div>
                <div class="info-group">
                    <span class="info-label">号:</span>
                    <input type="text" class="info-input" style="width: 60px;" value="001" readonly>
                </div>
                <div class="info-group">
                    <span class="info-label">日期:</span>
                    <input type="date" class="info-input" value="2024-01-15" readonly>
                </div>
                <div class="info-group">
                    <span class="info-label">附件:</span>
                    <input type="number" class="info-input" style="width: 40px;" value="2" readonly>
                    <span class="info-label">张</span>
                </div>
                <div style="flex: 1;"></div>
                <div class="info-group">
                    <span class="info-label">制单人:</span>
                    <span>张会计</span>
                </div>
            </div>

            <!-- 凭证表格 -->
            <div class="voucher-table-container">
                <table class="voucher-table">
                    <thead>
                        <tr>
                            <th style="width: 30px;">序号</th>
                            <th style="width: 200px;">摘要</th>
                            <th style="width: 250px;">会计科目</th>
                            <th style="width: 80px;">借方金额</th>
                            <th style="width: 80px;">贷方金额</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td style="text-align: center;">1</td>
                            <td>
                                <input type="text" class="cell-input" value="采购原材料" readonly>
                            </td>
                            <td>
                                <input type="text" class="cell-input" value="1402 - 原材料" readonly>
                            </td>
                            <td>
                                <input type="text" class="cell-input" value="15,680.00" readonly style="text-align: right;">
                            </td>
                            <td>
                                <input type="text" class="cell-input" readonly>
                            </td>
                        </tr>
                        <tr>
                            <td style="text-align: center;">2</td>
                            <td>
                                <input type="text" class="cell-input" value="采购原材料" readonly>
                            </td>
                            <td>
                                <input type="text" class="cell-input" value="2201 - 应付账款" readonly>
                            </td>
                            <td>
                                <input type="text" class="cell-input" readonly>
                            </td>
                            <td>
                                <input type="text" class="cell-input" value="15,680.00" readonly style="text-align: right;">
                            </td>
                        </tr>
                    </tbody>
                    <tfoot>
                        <tr style="background: #e3f2fd; font-weight: bold; color: #1565c0;">
                            <td colspan="3" style="text-align: center; border-top: 2px solid #1e88e5;">合计</td>
                            <td style="text-align: right; border-top: 2px solid #1e88e5;">15,680.00</td>
                            <td style="text-align: right; border-top: 2px solid #1e88e5;">15,680.00</td>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <!-- 签字区域 -->
            <div class="signature-area">
                <div class="signature-item">
                    <span class="signature-label">制单:</span>
                    <span class="signature-box">张会计</span>
                </div>
                <div class="signature-item">
                    <span class="signature-label">审核:</span>
                    <span class="signature-box">李主管</span>
                </div>
                <div class="signature-item">
                    <span class="signature-label">记账:</span>
                    <span class="signature-box"></span>
                </div>
                <div class="signature-item">
                    <span class="signature-label">出纳:</span>
                    <span class="signature-box"></span>
                </div>
            </div>

            <!-- 状态栏 -->
            <div style="background: #f5f5f5; border-top: 1px solid #e0e0e0; padding: 4px 15px; font-size: 11px; color: #666; display: flex; justify-content: space-between; align-items: center;">
                <span>查看模式 - 凭证已审核</span>
                <span id="current-time">2024-01-15 14:30:25</span>
            </div>
        </div>
    </div>

    <script>
        // 更新时间
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString();
            document.getElementById('current-time').textContent = timeString;
        }
        
        setInterval(updateTime, 1000);
        updateTime();
    </script>
</body>
</html>
