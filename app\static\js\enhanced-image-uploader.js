/**
 * 增强的图片上传组件
 * 支持拖拽上传、多图预览、图片评分等功能
 */

class EnhancedImageUploader {
    /**
     * 构造函数
     * @param {HTMLElement|string} container - 容器元素或其ID
     * @param {Object} options - 配置选项
     */
    constructor(container, options = {}) {
        // 容器元素
        this.container = typeof container === 'string' ? document.getElementById(container) : container;
        if (!this.container) {
            console.error('找不到上传组件容器');
            return;
        }

        // 默认选项
        this.defaultOptions = {
            referenceType: '',  // 引用类型
            referenceId: 0,     // 引用ID
            apiBaseUrl: '/daily-management/image-api',  // API基础URL
            maxFileSize: 5,     // 最大文件大小(MB)
            maxFiles: 10,       // 最大文件数量
            allowedTypes: ['image/jpeg', 'image/png', 'image/gif'],  // 允许的文件类型
            showRating: true,   // 是否显示评分
            showPreview: true,  // 是否显示预览
            previewSize: 150,   // 预览图大小
            dropZoneText: window.i18n?.dropZoneText || '拖拽图片到此处上传，或点击选择图片',  // 拖拽区文本
            uploadButtonText: window.i18n?.uploadButtonText || '选择图片',  // 上传按钮文本
            loadExisting: true  // 是否加载已有图片
        };

        // 合并选项
        this.options = { ...this.defaultOptions, ...options };

        // 事件监听器列表
        this.eventListeners = [];

        // 初始化
        this.init();
    }

    /**
     * 初始化上传组件
     */
    init() {
        // 创建DOM结构
        this.createDOMStructure();

        // 初始化事件监听
        this.initEventListeners();

        // 加载已有图片
        if (this.options.loadExisting) {
            this.loadExistingImages();
        }
    }

    /**
     * 创建DOM结构
     */
    createDOMStructure() {
        // 清空容器
        this.container.innerHTML = '';

        // 添加样式
        this.addStyles();

        // 创建拖拽区
        this.dropZone = document.createElement('div');
        this.dropZone.className = 'enhanced-uploader-dropzone';
        this.dropZone.innerHTML = `
            <div class="enhanced-uploader-icon">
                <i class="fas fa-cloud-upload-alt"></i>
            </div>
            <div class="enhanced-uploader-text">${this.options.dropZoneText}</div>
            <input type="file" class="enhanced-uploader-input" accept="image/*" multiple style="display: none;">
        `;
        this.container.appendChild(this.dropZone);

        // 获取文件输入元素
        this.fileInput = this.dropZone.querySelector('.enhanced-uploader-input');

        // 创建预览区
        this.previewContainer = document.createElement('div');
        this.previewContainer.className = 'enhanced-uploader-preview';
        this.container.appendChild(this.previewContainer);

        // 创建上传状态区
        this.statusContainer = document.createElement('div');
        this.statusContainer.className = 'enhanced-uploader-status';
        this.container.appendChild(this.statusContainer);
    }

    /**
     * 添加样式
     */
    addStyles() {
        // 检查是否已添加样式
        if (document.getElementById('enhanced-uploader-styles')) {
            return;
        }

        // 创建样式元素
        const style = document.createElement('style');
        style.id = 'enhanced-uploader-styles';
        style.textContent = `
            .enhanced-uploader-dropzone {
                border: 2px dashed #ccc;
                border-radius: 8px;
                padding: 20px;
                text-align: center;
                cursor: pointer;
                transition: all 0.3s;
                margin-bottom: 15px;
                background-color: #f8f9fa;
            }

            .enhanced-uploader-dropzone:hover,
            .enhanced-uploader-dropzone.dragover {
                border-color: #4e73df;
                background-color: #eef2ff;
            }

            .enhanced-uploader-icon {
                font-size: 2rem;
                color: #4e73df;
                margin-bottom: 10px;
            }

            .enhanced-uploader-text {
                color: #666;
                margin-bottom: 10px;
            }

            .enhanced-uploader-preview {
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
                margin-bottom: 15px;
            }

            .enhanced-uploader-item {
                position: relative;
                width: ${this.options.previewSize}px;
                height: ${this.options.previewSize}px;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                transition: all 0.3s;
            }

            .enhanced-uploader-item:hover {
                transform: translateY(-3px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            }

            .enhanced-uploader-item img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

            .enhanced-uploader-item-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                opacity: 0;
                transition: opacity 0.3s;
            }

            .enhanced-uploader-item:hover .enhanced-uploader-item-overlay {
                opacity: 1;
            }

            .enhanced-uploader-item-actions {
                display: flex;
                gap: 10px;
                margin-top: 5px;
            }

            .enhanced-uploader-item-action {
                color: white;
                background: rgba(0,0,0,0.5);
                border-radius: 50%;
                width: 30px;
                height: 30px;
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                transition: all 0.2s;
            }

            .enhanced-uploader-item-action:hover {
                background: rgba(0,0,0,0.8);
                transform: scale(1.1);
            }

            .enhanced-uploader-rating {
                display: flex;
                margin-top: 5px;
            }

            .enhanced-uploader-rating i {
                color: #f8ce0b;
                margin: 0 2px;
                cursor: pointer;
            }

            .enhanced-uploader-status {
                margin-top: 10px;
                padding: 10px;
                border-radius: 5px;
                display: none;
            }

            .enhanced-uploader-status.success {
                background-color: #e6f7ee;
                color: #1f9d55;
                display: block;
            }

            .enhanced-uploader-status.error {
                background-color: #fde8e8;
                color: #e53e3e;
                display: block;
            }

            .enhanced-uploader-progress {
                height: 5px;
                background-color: #e9ecef;
                border-radius: 3px;
                margin-top: 5px;
                overflow: hidden;
            }

            .enhanced-uploader-progress-bar {
                height: 100%;
                background-color: #4e73df;
                width: 0;
                transition: width 0.3s;
            }
        `;

        // 添加到文档头部
        document.head.appendChild(style);
    }

    /**
     * 初始化事件监听
     */
    initEventListeners() {
        // 点击拖拽区触发文件选择
        this.addEventListenerWithCleanup(this.dropZone, 'click', () => {
            this.fileInput.click();
        });

        // 文件选择变化
        this.addEventListenerWithCleanup(this.fileInput, 'change', (e) => {
            if (e.target.files.length > 0) {
                this.handleFiles(e.target.files);
                // 清空文件输入，以便可以重复选择同一文件
                e.target.value = '';
            }
        });

        // 拖拽相关事件
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            this.addEventListenerWithCleanup(this.dropZone, eventName, (e) => {
                e.preventDefault();
                e.stopPropagation();
            });
        });

        // 拖拽进入和悬停
        ['dragenter', 'dragover'].forEach(eventName => {
            this.addEventListenerWithCleanup(this.dropZone, eventName, () => {
                this.dropZone.classList.add('dragover');
            });
        });

        // 拖拽离开和放下
        ['dragleave', 'drop'].forEach(eventName => {
            this.addEventListenerWithCleanup(this.dropZone, eventName, () => {
                this.dropZone.classList.remove('dragover');
            });
        });

        // 拖拽放下文件
        this.addEventListenerWithCleanup(this.dropZone, 'drop', (e) => {
            if (e.dataTransfer.files.length > 0) {
                this.handleFiles(e.dataTransfer.files);
            }
        });
    }

    /**
     * 添加事件监听器并记录，以便后续清理
     * @param {HTMLElement} element - DOM元素
     * @param {string} eventName - 事件名称
     * @param {Function} handler - 事件处理函数
     */
    addEventListenerWithCleanup(element, eventName, handler) {
        element.addEventListener(eventName, handler);
        this.eventListeners.push({ element, eventName, handler });
    }

    /**
     * 处理文件
     * @param {FileList} files - 文件列表
     */
    handleFiles(files) {
        // 检查文件数量
        if (files.length > this.options.maxFiles) {
            this.showStatus(window.i18n?.maxFilesError?.replace('{0}', this.options.maxFiles) || `最多只能上传${this.options.maxFiles}张图片`, 'error');
            return;
        }

        // 处理每个文件
        Array.from(files).forEach(file => {
            this.processFile(file);
        });
    }

    /**
     * 处理单个文件
     * @param {File} file - 文件对象
     */
    processFile(file) {
        // 检查文件类型
        if (!this.options.allowedTypes.includes(file.type)) {
            this.showStatus(window.i18n?.unsupportedFileType?.replace('{0}', file.type) || `不支持的文件类型: ${file.type}`, 'error');
            return;
        }

        // 检查文件大小
        if (file.size > this.options.maxFileSize * 1024 * 1024) {
            this.showStatus(window.i18n?.fileTooLarge?.replace('{0}', this.options.maxFileSize) || `文件过大，最大允许${this.options.maxFileSize}MB`, 'error');
            return;
        }

        // 创建预览
        if (this.options.showPreview) {
            this.createPreview(file);
        }

        // 上传文件
        this.uploadFile(file);
    }

    /**
     * 创建文件预览
     * @param {File} file - 文件对象
     */
    createPreview(file) {
        const reader = new FileReader();

        reader.onload = (e) => {
            // 创建预览项
            const previewItem = document.createElement('div');
            previewItem.className = 'enhanced-uploader-item';
            previewItem.dataset.filename = file.name;

            // 创建预览图
            const img = document.createElement('img');
            img.src = e.target.result;
            img.alt = file.name;
            previewItem.appendChild(img);

            // 创建覆盖层
            const overlay = document.createElement('div');
            overlay.className = 'enhanced-uploader-item-overlay';

            // 创建操作按钮
            const actions = document.createElement('div');
            actions.className = 'enhanced-uploader-item-actions';

            // 查看按钮
            const viewBtn = document.createElement('div');
            viewBtn.className = 'enhanced-uploader-item-action';
            viewBtn.innerHTML = '<i class="fas fa-eye"></i>';
            viewBtn.addEventListener('click', () => {
                this.viewImage(e.target.result, file.name);
            });
            actions.appendChild(viewBtn);

            // 删除按钮
            const deleteBtn = document.createElement('div');
            deleteBtn.className = 'enhanced-uploader-item-action';
            deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
            deleteBtn.addEventListener('click', () => {
                // 标记为正在上传，防止删除
                if (previewItem.dataset.uploading === 'true') {
                    this.showStatus('正在上传，无法删除', 'error');
                    return;
                }

                // 如果已上传，则调用删除API
                if (previewItem.dataset.photoId) {
                    this.deleteImage(previewItem.dataset.photoId, previewItem);
                } else {
                    // 否则直接移除预览
                    previewItem.remove();
                }
            });
            actions.appendChild(deleteBtn);

            overlay.appendChild(actions);

            // 如果启用评分，添加评分组件
            if (this.options.showRating) {
                const rating = document.createElement('div');
                rating.className = 'enhanced-uploader-rating';

                // 创建5个星星
                for (let i = 1; i <= 5; i++) {
                    const star = document.createElement('i');
                    star.className = i <= 3 ? 'fas fa-star' : 'far fa-star'; // 默认3星
                    star.dataset.rating = i;

                    star.addEventListener('click', (e) => {
                        e.stopPropagation();

                        // 如果没有photoId，说明还没上传完成
                        if (!previewItem.dataset.photoId) {
                            this.showStatus('请等待上传完成后再评分', 'error');
                            return;
                        }

                        // 更新星星显示
                        const stars = rating.querySelectorAll('i');
                        stars.forEach((s, index) => {
                            s.className = index < i ? 'fas fa-star' : 'far fa-star';
                        });

                        // 更新评分
                        this.updateRating(previewItem.dataset.photoId, i);
                    });

                    rating.appendChild(star);
                }

                overlay.appendChild(rating);
            }

            previewItem.appendChild(overlay);

            // 添加到预览容器
            this.previewContainer.appendChild(previewItem);

            // 标记为正在上传
            previewItem.dataset.uploading = 'true';

            // 添加进度条
            const progress = document.createElement('div');
            progress.className = 'enhanced-uploader-progress';
            const progressBar = document.createElement('div');
            progressBar.className = 'enhanced-uploader-progress-bar';
            progress.appendChild(progressBar);
            previewItem.appendChild(progress);
        };

        reader.readAsDataURL(file);
    }

    /**
     * 上传文件
     * @param {File} file - 文件对象
     */
    uploadFile(file) {
        // 创建FormData
        const formData = new FormData();
        formData.append('photo', file);
        formData.append('description', '');

        // 获取预览项
        const previewItem = this.previewContainer.querySelector(`[data-filename="${file.name}"]`);
        const progressBar = previewItem ? previewItem.querySelector('.enhanced-uploader-progress-bar') : null;

        // 创建XHR对象
        const xhr = new XMLHttpRequest();

        // 监听上传进度
        xhr.upload.addEventListener('progress', (e) => {
            if (e.lengthComputable && progressBar) {
                const percent = (e.loaded / e.total) * 100;
                progressBar.style.width = `${percent}%`;
            }
        });

        // 监听上传完成
        xhr.addEventListener('load', () => {
            if (xhr.status >= 200 && xhr.status < 300) {
                try {
                    const response = JSON.parse(xhr.responseText);

                    if (previewItem) {
                        // 移除进度条
                        const progress = previewItem.querySelector('.enhanced-uploader-progress');
                        if (progress) {
                            progress.remove();
                        }

                        // 标记为已上传
                        previewItem.dataset.uploading = 'false';
                        previewItem.dataset.photoId = response.id;
                    }

                    this.showStatus(`${file.name} 上传成功`, 'success');
                } catch (error) {
                    this.showStatus(`解析响应失败: ${error.message}`, 'error');
                }
            } else {
                this.showStatus(`上传失败: ${xhr.statusText}`, 'error');
            }
        });

        // 监听上传错误
        xhr.addEventListener('error', () => {
            this.showStatus(`上传失败: 网络错误`, 'error');

            if (previewItem) {
                previewItem.dataset.uploading = 'false';
            }
        });

        // 打开连接
        xhr.open('POST', `${this.options.apiBaseUrl}/photos/${this.options.referenceType}/${this.options.referenceId}`);

        // 添加CSRF令牌
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            xhr.setRequestHeader('X-CSRFToken', csrfToken.getAttribute('content'));
        }

        // 发送请求
        xhr.send(formData);
    }

    /**
     * 加载已有图片
     */
    loadExistingImages() {
        // 发送请求获取已有图片
        fetch(`${this.options.apiBaseUrl}/photos/${this.options.referenceType}/${this.options.referenceId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.photos && data.photos.length > 0) {
                    data.photos.forEach(photo => {
                        this.createExistingImagePreview(photo);
                    });
                }
            })
            .catch(error => {
                console.error('加载已有图片失败:', error);
            });
    }

    /**
     * 创建已有图片的预览
     * @param {Object} photo - 照片对象
     */
    createExistingImagePreview(photo) {
        // 创建预览项
        const previewItem = document.createElement('div');
        previewItem.className = 'enhanced-uploader-item';
        previewItem.dataset.photoId = photo.id;

        // 创建预览图
        const img = document.createElement('img');
        img.src = photo.file_path;
        img.alt = photo.file_name || '照片';
        previewItem.appendChild(img);

        // 创建覆盖层
        const overlay = document.createElement('div');
        overlay.className = 'enhanced-uploader-item-overlay';

        // 创建操作按钮
        const actions = document.createElement('div');
        actions.className = 'enhanced-uploader-item-actions';

        // 查看按钮
        const viewBtn = document.createElement('div');
        viewBtn.className = 'enhanced-uploader-item-action';
        viewBtn.innerHTML = '<i class="fas fa-eye"></i>';
        viewBtn.addEventListener('click', () => {
            this.viewImage(photo.file_path, photo.file_name);
        });
        actions.appendChild(viewBtn);

        // 删除按钮
        const deleteBtn = document.createElement('div');
        deleteBtn.className = 'enhanced-uploader-item-action';
        deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
        deleteBtn.addEventListener('click', () => {
            this.deleteImage(photo.id, previewItem);
        });
        actions.appendChild(deleteBtn);

        overlay.appendChild(actions);

        // 如果启用评分，添加评分组件
        if (this.options.showRating) {
            const rating = document.createElement('div');
            rating.className = 'enhanced-uploader-rating';

            // 创建5个星星
            for (let i = 1; i <= 5; i++) {
                const star = document.createElement('i');
                star.className = i <= (photo.rating || 3) ? 'fas fa-star' : 'far fa-star';
                star.dataset.rating = i;

                star.addEventListener('click', (e) => {
                    e.stopPropagation();

                    // 更新星星显示
                    const stars = rating.querySelectorAll('i');
                    stars.forEach((s, index) => {
                        s.className = index < i ? 'fas fa-star' : 'far fa-star';
                    });

                    // 更新评分
                    this.updateRating(photo.id, i);
                });

                rating.appendChild(star);
            }

            overlay.appendChild(rating);
        }

        previewItem.appendChild(overlay);

        // 添加到预览容器
        this.previewContainer.appendChild(previewItem);
    }

    /**
     * 查看图片
     * @param {string} src - 图片源
     * @param {string} name - 图片名称
     */
    viewImage(src, name) {
        // 检查是否有SweetAlert2
        if (typeof Swal === 'undefined') {
            // 如果没有SweetAlert2，使用原生方法
            window.open(src, '_blank');
            return;
        }

        // 使用SweetAlert2显示图片
        Swal.fire({
            title: name,
            imageUrl: src,
            imageAlt: name,
            confirmButtonText: '关闭',
            width: 'auto'
        });
    }

    /**
     * 删除图片
     * @param {number} photoId - 照片ID
     * @param {HTMLElement} previewItem - 预览项元素
     */
    deleteImage(photoId, previewItem) {
        // 确认删除
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: '确认删除',
                text: '确定要删除这张照片吗？',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: '删除',
                cancelButtonText: '取消'
            }).then((result) => {
                if (result.isConfirmed) {
                    this.performDeleteImage(photoId, previewItem);
                }
            });
        } else {
            if (confirm('确定要删除这张照片吗？')) {
                this.performDeleteImage(photoId, previewItem);
            }
        }
    }

    /**
     * 执行删除图片
     * @param {number} photoId - 照片ID
     * @param {HTMLElement} previewItem - 预览项元素
     */
    performDeleteImage(photoId, previewItem) {
        // 发送删除请求
        fetch(`${this.options.apiBaseUrl}/photos/${photoId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 移除预览项
                previewItem.remove();
                this.showStatus('照片已删除', 'success');
            } else {
                this.showStatus(`删除失败: ${data.error || '未知错误'}`, 'error');
            }
        })
        .catch(error => {
            this.showStatus(`删除失败: ${error.message}`, 'error');
        });
    }

    /**
     * 更新评分
     * @param {number} photoId - 照片ID
     * @param {number} rating - 评分值
     */
    updateRating(photoId, rating) {
        // 发送更新请求
        fetch(`/daily-management/api/v2/photos/${photoId}/rating`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            },
            body: JSON.stringify({ rating })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showStatus('评分已更新', 'success');
            } else {
                this.showStatus(`更新评分失败: ${data.error || '未知错误'}`, 'error');
            }
        })
        .catch(error => {
            this.showStatus(`更新评分失败: ${error.message}`, 'error');
        });
    }

    /**
     * 显示状态信息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型（success/error）
     */
    showStatus(message, type) {
        this.statusContainer.textContent = message;
        this.statusContainer.className = `enhanced-uploader-status ${type}`;

        // 3秒后自动隐藏
        setTimeout(() => {
            this.statusContainer.style.display = 'none';
        }, 3000);
    }

    /**
     * 销毁组件，清理事件监听器
     */
    destroy() {
        // 清理所有事件监听器
        this.eventListeners.forEach(({ element, eventName, handler }) => {
            element.removeEventListener(eventName, handler);
        });

        // 清空事件监听器列表
        this.eventListeners = [];

        // 清空容器
        this.container.innerHTML = '';
    }
}
