/**
 * 事件处理器管理器 - 统一管理所有事件处理器，避免重复定义
 * 这个脚本应该在所有其他事件处理器脚本之前加载
 */

(function() {
    'use strict';
    
    // 防止重复加载
    if (window.EventHandlerManager) {
        return;
    }
    
    // 全局事件处理器管理器
    window.EventHandlerManager = {
        initialized: false,
        handlers: new Map(),
        
        // 安全执行函数（全局唯一）
        safeExecute: function(code) {
            if (!code) return;

            code = code.trim();

            console.log('🔍 EventHandlerManager 尝试执行代码:', code);

            // 处理confirm调用 - 改进的正则表达式
            if (code.includes('confirm(')) {
                console.log('🔍 检测到confirm调用:', code);

                // 多种confirm调用格式的正则表达式
                const confirmPatterns = [
                    /confirm\s*\(\s*['"](.*?)['"]\s*\)/,  // confirm('message')
                    /confirm\s*\(\s*"([^"]*)"\s*\)/,      // confirm("message")
                    /confirm\s*\(\s*'([^']*)'\s*\)/,      // confirm('message')
                    /confirm\s*\(\s*([^)]+)\s*\)/         // confirm(variable)
                ];

                for (let pattern of confirmPatterns) {
                    const match = code.match(pattern);
                    if (match) {
                        const message = match[1] || '确定要执行此操作吗？';
                        console.log('✅ 执行confirm，消息:', message);
                        return confirm(message);
                    }
                }

                // 如果没有匹配到参数，使用默认消息
                console.log('⚠️ confirm调用格式不标准，使用默认消息');
                return confirm('确定要执行此操作吗？');
            }

            // 处理return语句
            if (code.startsWith('return ')) {
                const returnCode = code.substring(7);
                console.log('🔍 处理return语句:', returnCode);
                return this.safeExecute(returnCode);
            }

            // 处理简单的函数调用
            const funcMatch = code.match(/^(\w+)\s*\(([^)]*)\)$/);
            if (funcMatch) {
                const funcName = funcMatch[1];
                console.log('🔍 检测到函数调用:', funcName);

                if (typeof window[funcName] === 'function') {
                    try {
                        console.log('✅ 执行全局函数:', funcName);
                        return window[funcName]();
                    } catch (e) {
                        console.warn('函数执行失败:', funcName, e);
                    }
                }
            }

            // 处理window对象方法
            if (code === 'window.print()') {
                console.log('✅ 执行window.print()');
                return window.print();
            }
            if (code === 'window.close()') {
                console.log('✅ 执行window.close()');
                return window.close();
            }
            if (code === 'history.back()') {
                console.log('✅ 执行history.back()');
                return history.back();
            }
            if (code === 'location.reload()') {
                console.log('✅ 执行location.reload()');
                return location.reload();
            }

            console.warn('❌ 不支持的代码执行:', code);
            return false;
        },
        
        // 注册事件处理器
        register: function(name, handler) {
            if (this.handlers.has(name)) {
                console.warn(`事件处理器 ${name} 已存在，将被覆盖`);
            }
            this.handlers.set(name, handler);
        },
        
        // 获取事件处理器
        get: function(name) {
            return this.handlers.get(name);
        },
        
        // 初始化所有事件处理器
        init: function() {
            if (this.initialized) {
                return;
            }
            
            this.bindDataOnclickHandlers();
            this.bindDataOnsubmitHandlers();
            this.bindDataOnchangeHandlers();
            this.bindCriticalHandlers();
            
            this.initialized = true;
            console.log('✅ 事件处理器管理器已初始化');
        },
        
        // 绑定data-onclick处理器
        bindDataOnclickHandlers: function() {
            document.querySelectorAll('[data-onclick]').forEach(element => {
                if (!element.hasAttribute('data-handler-bound')) {
                    const code = element.getAttribute('data-onclick');
                    element.addEventListener('click', (e) => {
                        e.preventDefault();
                        try {
                            this.safeExecute(code);
                        } catch (error) {
                            console.error('onclick处理器执行失败:', error, '代码:', code);
                        }
                    });
                    element.setAttribute('data-handler-bound', 'true');
                }
            });
        },
        
        // 绑定data-onsubmit处理器
        bindDataOnsubmitHandlers: function() {
            document.querySelectorAll('[data-onsubmit]').forEach(form => {
                if (!form.hasAttribute('data-handler-bound')) {
                    const code = form.getAttribute('data-onsubmit');
                    form.addEventListener('submit', (e) => {
                        try {
                            const result = this.safeExecute(code);
                            if (result === false) {
                                e.preventDefault();
                            }
                        } catch (error) {
                            console.error('onsubmit处理器执行失败:', error, '代码:', code);
                            e.preventDefault();
                        }
                    });
                    form.setAttribute('data-handler-bound', 'true');
                }
            });
        },
        
        // 绑定data-onchange处理器
        bindDataOnchangeHandlers: function() {
            document.querySelectorAll('[data-onchange]').forEach(element => {
                if (!element.hasAttribute('data-handler-bound')) {
                    const code = element.getAttribute('data-onchange');
                    element.addEventListener('change', (e) => {
                        try {
                            this.safeExecute(code);
                        } catch (error) {
                            console.error('onchange处理器执行失败:', error, '代码:', code);
                        }
                    });
                    element.setAttribute('data-handler-bound', 'true');
                }
            });
        },
        
        // 绑定关键操作处理器
        bindCriticalHandlers: function() {
            // 处理确认操作
            document.querySelectorAll('[data-validation="critical"]').forEach(element => {
                if (!element.hasAttribute('data-handler-bound')) {
                    element.addEventListener('click', (e) => {
                        const message = element.getAttribute('data-message') || '确定要执行此操作吗？';
                        if (!confirm(message)) {
                            e.preventDefault();
                        }
                    });
                    element.setAttribute('data-handler-bound', 'true');
                }
            });
            
            // 处理表单确认
            document.querySelectorAll('form[data-validation="critical"]').forEach(form => {
                if (!form.hasAttribute('data-handler-bound')) {
                    form.addEventListener('submit', (e) => {
                        const message = form.getAttribute('data-message') || '确定要提交此表单吗？';
                        if (!confirm(message)) {
                            e.preventDefault();
                        }
                    });
                    form.setAttribute('data-handler-bound', 'true');
                }
            });
        },
        
        // 重新扫描并绑定新元素
        rescan: function() {
            this.bindDataOnclickHandlers();
            this.bindDataOnsubmitHandlers();
            this.bindDataOnchangeHandlers();
            this.bindCriticalHandlers();
        }
    };
    
    // 为了向后兼容，将safeExecute暴露到全局
    if (!window.safeExecute) {
        window.safeExecute = function(code) {
            return window.EventHandlerManager.safeExecute(code);
        };
    }
    
    if (!window.safeExecuteCode) {
        window.safeExecuteCode = function(code) {
            return window.EventHandlerManager.safeExecute(code);
        };
    }
    
    // DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            window.EventHandlerManager.init();
        });
    } else {
        // DOM已经加载完成
        window.EventHandlerManager.init();
    }
    
    // 监听动态内容变化
    if (window.MutationObserver) {
        const observer = new MutationObserver(function(mutations) {
            let shouldRescan = false;
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    for (let node of mutation.addedNodes) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            if (node.hasAttribute && (
                                node.hasAttribute('data-onclick') ||
                                node.hasAttribute('data-onsubmit') ||
                                node.hasAttribute('data-onchange') ||
                                node.hasAttribute('data-validation')
                            )) {
                                shouldRescan = true;
                                break;
                            }
                            // 检查子元素
                            if (node.querySelector && node.querySelector('[data-onclick], [data-onsubmit], [data-onchange], [data-validation]')) {
                                shouldRescan = true;
                                break;
                            }
                        }
                    }
                }
            });
            
            if (shouldRescan) {
                setTimeout(() => window.EventHandlerManager.rescan(), 100);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    console.log('✅ 事件处理器管理器已加载');
})();
