/**
 * 图标字体样式 - 替代Font Awesome
 * 使用Unicode符号和CSS图标
 */

.icon {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}

/* 基础图标类 */
.fa {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}

/* 图标尺寸 */
.fa-lg { font-size: 1.33333em; }
.fa-xl { font-size: 1.5em; }
.fa-2x { font-size: 2em; }
.fa-3x { font-size: 3em; }

/* 常用图标 - 使用Unicode符号 */
.fa-cutlery:before { content: "🍴"; }
.fa-bars:before { content: "☰"; }
.fa-sign-in:before { content: "→"; }
.fa-user-plus:before { content: "👤+"; }
.fa-arrow-down:before { content: "↓"; }
.fa-arrow-right:before { content: "→"; }
.fa-search:before { content: "🔍"; }
.fa-qrcode:before { content: "⚏"; }
.fa-print:before { content: "🖨"; }
.fa-shopping-cart:before { content: "🛒"; }
.fa-exchange:before { content: "⇄"; }
.fa-list:before { content: "📋"; }
.fa-users:before { content: "👥"; }
.fa-file-text-o:before { content: "📄"; }
.fa-shield:before { content: "🛡"; }
.fa-line-chart:before { content: "📈"; }
.fa-mobile:before { content: "📱"; }
.fa-phone:before { content: "📞"; }
.fa-envelope:before { content: "✉"; }
.fa-map-marker:before { content: "📍"; }
.fa-weixin:before { content: "💬"; }
.fa-weibo:before { content: "🌐"; }
.fa-qq:before { content: "💬"; }
.fa-gift:before { content: "🎁"; }
.fa-image:before { content: "🖼"; }
.fa-play-circle:before { content: "▶"; }
.fa-sync-alt:before { content: "🔄"; }
.fa-clipboard-list:before { content: "📋"; }
.fa-calendar-day:before { content: "📅"; }
.fa-vial:before { content: "🧪"; }
.fa-calendar-alt:before { content: "📅"; }

/* CSS图标 - 更精确的图标 */
.icon-menu {
  width: 24px;
  height: 24px;
  position: relative;
  display: inline-block;
}

.icon-menu:before,
.icon-menu:after,
.icon-menu {
  background: currentColor;
  border-radius: 2px;
  height: 3px;
  width: 24px;
}

.icon-menu:before,
.icon-menu:after {
  content: '';
  position: absolute;
  left: 0;
}

.icon-menu:before {
  top: -8px;
}

.icon-menu:after {
  bottom: -8px;
}

/* 箭头图标 */
.icon-arrow-right {
  width: 0;
  height: 0;
  border-left: 8px solid currentColor;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  display: inline-block;
}

.icon-arrow-down {
  width: 0;
  height: 0;
  border-top: 8px solid currentColor;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  display: inline-block;
}

/* 搜索图标 */
.icon-search {
  width: 20px;
  height: 20px;
  border: 2px solid currentColor;
  border-radius: 50%;
  position: relative;
  display: inline-block;
}

.icon-search:after {
  content: '';
  position: absolute;
  top: 15px;
  left: 15px;
  width: 8px;
  height: 2px;
  background: currentColor;
  transform: rotate(45deg);
}

/* 用户图标 */
.icon-user {
  width: 20px;
  height: 20px;
  border: 2px solid currentColor;
  border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
  position: relative;
  display: inline-block;
}

.icon-user:after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: -4px;
  width: 28px;
  height: 14px;
  border: 2px solid currentColor;
  border-radius: 50% 50% 0 0;
  border-bottom: none;
}

/* 购物车图标 */
.icon-cart {
  width: 20px;
  height: 16px;
  border: 2px solid currentColor;
  border-radius: 2px;
  position: relative;
  display: inline-block;
}

.icon-cart:before {
  content: '';
  position: absolute;
  top: -6px;
  left: -4px;
  width: 8px;
  height: 8px;
  border: 2px solid currentColor;
  border-bottom: none;
  border-right: none;
}

.icon-cart:after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 2px;
  width: 4px;
  height: 4px;
  border: 2px solid currentColor;
  border-radius: 50%;
  box-shadow: 10px 0 0 currentColor;
}

/* 电话图标 */
.icon-phone {
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-radius: 4px 4px 12px 12px;
  position: relative;
  display: inline-block;
}

.icon-phone:before {
  content: '';
  position: absolute;
  top: 2px;
  left: 4px;
  width: 6px;
  height: 1px;
  background: currentColor;
}

.icon-phone:after {
  content: '';
  position: absolute;
  bottom: 2px;
  left: 5px;
  width: 4px;
  height: 4px;
  border: 1px solid currentColor;
  border-radius: 50%;
}

/* 邮件图标 */
.icon-email {
  width: 20px;
  height: 14px;
  border: 2px solid currentColor;
  border-radius: 2px;
  position: relative;
  display: inline-block;
}

.icon-email:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 8px solid currentColor;
}

/* 位置图标 */
.icon-location {
  width: 16px;
  height: 20px;
  border: 2px solid currentColor;
  border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
  position: relative;
  display: inline-block;
}

.icon-location:after {
  content: '';
  position: absolute;
  top: 4px;
  left: 4px;
  width: 6px;
  height: 6px;
  border: 2px solid currentColor;
  border-radius: 50%;
}

/* 图表图标 */
.icon-chart {
  width: 20px;
  height: 16px;
  position: relative;
  display: inline-block;
}

.icon-chart:before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 4px;
  height: 8px;
  background: currentColor;
  box-shadow: 6px 4px 0 currentColor, 12px -2px 0 currentColor;
}

.icon-chart:after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 16px;
  height: 1px;
  background: currentColor;
  transform: rotate(15deg);
}

/* 盾牌图标 */
.icon-shield {
  width: 16px;
  height: 20px;
  border: 2px solid currentColor;
  border-radius: 50% 50% 0 0;
  position: relative;
  display: inline-block;
}

.icon-shield:after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 6px;
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 8px solid currentColor;
}

/* 移动设备图标 */
.icon-mobile {
  width: 12px;
  height: 20px;
  border: 2px solid currentColor;
  border-radius: 3px;
  position: relative;
  display: inline-block;
}

.icon-mobile:before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 6px;
  height: 1px;
  background: currentColor;
}

.icon-mobile:after {
  content: '';
  position: absolute;
  bottom: 2px;
  left: 4px;
  width: 2px;
  height: 2px;
  border: 1px solid currentColor;
  border-radius: 50%;
}

/* 礼品图标 */
.icon-gift {
  width: 18px;
  height: 16px;
  border: 2px solid currentColor;
  border-radius: 2px;
  position: relative;
  display: inline-block;
}

.icon-gift:before {
  content: '';
  position: absolute;
  top: -6px;
  left: 7px;
  width: 2px;
  height: 6px;
  background: currentColor;
}

.icon-gift:after {
  content: '';
  position: absolute;
  top: -8px;
  left: 2px;
  width: 12px;
  height: 4px;
  border: 2px solid currentColor;
  border-radius: 50%;
}

/* 响应式图标大小 */
@media (max-width: 768px) {
  .fa-2x { font-size: 1.5em; }
  .fa-3x { font-size: 2em; }
  
  .icon-menu,
  .icon-search,
  .icon-user,
  .icon-cart,
  .icon-phone,
  .icon-email {
    transform: scale(0.9);
  }
}
