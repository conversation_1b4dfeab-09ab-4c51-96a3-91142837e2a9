/* 本地中文字体定义 */
:root {
    --font-family-sans-serif: -apple-system, BlinkMacSystemFont, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", "Segoe UI", Roboto, "Helvetica Neue", <PERSON>l, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

/* 应用字体到全局 */
body {
    font-family: var(--font-family-sans-serif);
}

/* 应用字体到标题 */
h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
    font-family: var(--font-family-sans-serif);
    font-weight: 500;
}

/* 应用字体到按钮 */
.btn {
    font-family: var(--font-family-sans-serif);
}

/* 应用字体到表单元素 */
input, select, textarea, .form-control {
    font-family: var(--font-family-sans-serif);
}

/* 应用字体到导航 */
.navbar, .nav, .dropdown-menu {
    font-family: var(--font-family-sans-serif);
}

/* 应用字体到表格 */
.table {
    font-family: var(--font-family-sans-serif);
}

/* 应用字体到卡片 */
.card {
    font-family: var(--font-family-sans-serif);
}

/* 应用字体到模态框 */
.modal {
    font-family: var(--font-family-sans-serif);
}

/* 应用字体到提示框 */
.tooltip, .popover {
    font-family: var(--font-family-sans-serif);
}

/* 应用字体到面包屑导航 */
.breadcrumb {
    font-family: var(--font-family-sans-serif);
}

/* 应用字体到分页 */
.pagination {
    font-family: var(--font-family-sans-serif);
}

/* 应用字体到徽章 */
.badge {
    font-family: var(--font-family-sans-serif);
}

/* 应用字体到警告框 */
.alert {
    font-family: var(--font-family-sans-serif);
}

/* 应用字体到列表组 */
.list-group {
    font-family: var(--font-family-sans-serif);
}

/* 应用字体到进度条 */
.progress {
    font-family: var(--font-family-sans-serif);
}

/* 应用字体到导航标签 */
.nav-tabs, .nav-pills {
    font-family: var(--font-family-sans-serif);
}

/* 应用字体到折叠面板 */
.accordion {
    font-family: var(--font-family-sans-serif);
}

/* 应用字体到轮播图 */
.carousel {
    font-family: var(--font-family-sans-serif);
}

/* 应用字体到页脚 */
.footer {
    font-family: var(--font-family-sans-serif);
}
