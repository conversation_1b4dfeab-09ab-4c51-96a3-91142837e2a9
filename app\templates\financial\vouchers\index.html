{% extends "financial/base.html" %}

{% block title %}记账凭证{% endblock %}

{% block financial_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <!-- 专业财务凭证管理界面 -->
            <div class="card shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="mb-0 text-dark">记账凭证</h5>
                            <small class="text-muted">Accounting Vouchers</small>
                        </div>
                        <div class="col-md-6 text-right">
                            <!-- 简洁的操作按钮 -->
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('financial.create_voucher') }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus"></i> 新建
                                </a>
                                <a href="{{ url_for('financial.pending_stock_ins_for_voucher') }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-magic"></i> 自动生成
                                </a>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="showBatchModal()">
                                    <i class="fas fa-tasks"></i> 批量
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="exportVouchers()">
                                    <i class="fas fa-download"></i> 导出
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <!-- 专业的统计信息条 -->
                    <div class="bg-light border-bottom px-3 py-2">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <small class="text-muted d-block">总计</small>
                                <strong class="text-primary">{{ vouchers.total }}</strong>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted d-block">待审核</small>
                                <strong class="text-warning">{{ pending_count or 0 }}</strong>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted d-block">已审核</small>
                                <strong class="text-success">{{ approved_count or 0 }}</strong>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted d-block">本月金额</small>
                                <strong class="text-info">¥{{ "{:,.2f}".format(month_amount or 0) }}</strong>
                            </div>
                        </div>
                    </div>

                    <!-- 专业的搜索筛选区 -->
                    <div class="px-3 py-2 border-bottom bg-light">
                        <form method="GET" id="searchForm">
                            <div class="row align-items-end">
                                <div class="col-md-3">
                                    <label class="form-label text-muted small mb-1">搜索</label>
                                    <input type="text" class="form-control form-control-sm" name="keyword"
                                           value="{{ keyword }}" placeholder="凭证号/摘要">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label text-muted small mb-1">类型</label>
                                    <select class="form-control form-control-sm" name="voucher_type">
                                        <option value="">全部</option>
                                        <option value="收款凭证" {% if voucher_type == '收款凭证' %}selected{% endif %}>收款凭证</option>
                                        <option value="付款凭证" {% if voucher_type == '付款凭证' %}selected{% endif %}>付款凭证</option>
                                        <option value="转账凭证" {% if voucher_type == '转账凭证' %}selected{% endif %}>转账凭证</option>
                                        <option value="记账凭证" {% if voucher_type == '记账凭证' %}selected{% endif %}>记账凭证</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label text-muted small mb-1">状态</label>
                                    <select class="form-control form-control-sm" name="status">
                                        <option value="">全部</option>
                                        <option value="草稿" {% if status == '草稿' %}selected{% endif %}>草稿</option>
                                        <option value="待审核" {% if status == '待审核' %}selected{% endif %}>待审核</option>
                                        <option value="已审核" {% if status == '已审核' %}selected{% endif %}>已审核</option>
                                        <option value="已记账" {% if status == '已记账' %}selected{% endif %}>已记账</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label text-muted small mb-1">开始日期</label>
                                    <input type="date" class="form-control form-control-sm" name="start_date"
                                           value="{{ start_date }}">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label text-muted small mb-1">结束日期</label>
                                    <input type="date" class="form-control form-control-sm" name="end_date"
                                           value="{{ end_date }}">
                                </div>
                                <div class="col-md-1">
                                    <button type="submit" class="btn btn-primary btn-sm w-100">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 专业的凭证列表 -->
                    {% if vouchers.items %}
                    <div class="table-responsive">
                        <table class="table table-sm mb-0" id="vouchersTable">
                            <thead class="bg-light">
                                <tr>
                                    <th style="width: 30px;">
                                        <input type="checkbox" id="selectAll">
                                    </th>
                                    <th style="width: 150px;">凭证号</th>
                                    <th style="width: 60px;">日期</th>
                                    <th style="width: 60px;">类型</th>
                                    <th>摘要</th>
                                    <th style="width: 80px;">金额</th>
                                    <th style="width: 60px;">状态</th>
                                    <th style="width: 120px;">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for voucher in vouchers.items %}
                                <tr class="border-bottom" data-voucher-id="{{ voucher.id }}">
                                    <td class="checkbox-cell">
                                        <input type="checkbox" class="voucher-checkbox" value="{{ voucher.id }}">
                                    </td>
                                    <td style="width: 150px; max-width: 150px;">
                                        <a href="{{ url_for('financial.view_voucher', id=voucher.id) }}">
                                            {{ voucher.voucher_number }}
                                        </a>
                                        {% if voucher.attachment_count and voucher.attachment_count > 0 %}
                                            <i class="fas fa-paperclip" title="有附件" style="font-size: 10px; color: #999; margin-left: 2px;"></i>
                                        {% endif %}
                                    </td>
                                    <td style="text-align: center; color: #666;">
                                        {{ voucher.voucher_date.strftime('%m-%d') }}
                                    </td>
                                    <td style="text-align: center;">
                                        <span style="font-size: 11px; color: #666;">{{ voucher.voucher_type }}</span>
                                    </td>
                                    <td>
                                        <div style="max-width: 250px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="{{ voucher.summary }}">
                                            {{ voucher.summary }}
                                        </div>
                                    </td>
                                    <td class="amount-cell">
                                        {{ "{:,.2f}".format(voucher.total_amount) }}
                                    </td>
                                    <td style="text-align: center;">
                                        {% if voucher.status == '草稿' %}
                                            <span class="badge badge-secondary">{{ voucher.status }}</span>
                                        {% elif voucher.status == '待审核' %}
                                            <span class="badge badge-warning">{{ voucher.status }}</span>
                                        {% elif voucher.status == '已审核' %}
                                            <span class="badge badge-success">{{ voucher.status }}</span>
                                        {% elif voucher.status == '已记账' %}
                                            <span class="badge badge-primary">{{ voucher.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td style="text-align: center;">
                                        <div class="btn-group">
                                            <!-- 查看按钮 -->
                                            <a href="{{ url_for('financial.view_voucher', id=voucher.id) }}"
                                               class="btn btn-outline-info" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </a>

                                            <!-- 编辑按钮 -->
                                            {% if voucher.status in ['草稿', '待审核'] %}
                                            <a href="{{ url_for('financial.edit_voucher', id=voucher.id) }}"
                                               class="btn btn-outline-warning" title="编辑凭证">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% endif %}

                                            <!-- 文本视图按钮 -->
                                            <a href="{{ url_for('financial.voucher_text_view', id=voucher.id) }}"
                                               class="btn btn-outline-secondary" title="文本视图">
                                                <i class="fas fa-file-alt"></i>
                                            </a>

                                            <!-- 审核按钮 -->
                                            {% if voucher.status == '待审核' %}
                                            <button type="button" class="btn btn-outline-success"
                                                    onclick="reviewVoucher({{ voucher.id }})" title="审核通过">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            {% endif %}

                                            <!-- 复制按钮 -->
                                            <button type="button" class="btn btn-outline-primary"
                                                    onclick="copyVoucher({{ voucher.id }})" title="复制凭证">
                                                <i class="fas fa-copy"></i>
                                            </button>

                                            <!-- 删除按钮 -->
                                            {% if voucher.status in ['草稿', '待审核'] %}
                                            <button type="button" class="btn btn-outline-danger"
                                                    onclick="deleteVoucher({{ voucher.id }})" title="删除凭证">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    {% if vouchers.pages > 1 %}
                    <nav aria-label="凭证分页">
                        <ul class="pagination justify-content-center">
                            {% if vouchers.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('financial.vouchers_index', page=vouchers.prev_num, keyword=keyword, voucher_type=voucher_type, status=status, start_date=start_date, end_date=end_date) }}">上一页</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in vouchers.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != vouchers.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('financial.vouchers_index', page=page_num, keyword=keyword, voucher_type=voucher_type, status=status, start_date=start_date, end_date=end_date) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if vouchers.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('financial.vouchers_index', page=vouchers.next_num, keyword=keyword, voucher_type=voucher_type, status=status, start_date=start_date, end_date=end_date) }}">下一页</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle"></i> 暂无财务凭证数据
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量生成凭证模态框 -->
<div class="modal fade" id="batchGenerateModal" tabindex="-1" role="dialog" aria-labelledby="batchGenerateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchGenerateModalLabel">批量生成财务凭证</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>批量生成说明：</strong>
                    <ul class="mb-0 mt-2">
                        <li>系统将自动查找已财务确认但未生成凭证的入库单</li>
                        <li>为每个入库单生成对应的应付账款和财务凭证</li>
                        <li>生成的凭证将自动审核通过</li>
                        <li>请确保会计科目设置正确（原材料：1402，应付账款：2201）</li>
                    </ul>
                </div>

                <form id="batchGenerateForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="startDate">开始日期</label>
                                <input type="date" class="form-control" id="startDate" name="start_date" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="endDate">结束日期</label>
                                <input type="date" class="form-control" id="endDate" name="end_date" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="autoReview" name="auto_review" checked>
                            <label class="form-check-label" for="autoReview">
                                自动审核生成的凭证
                            </label>
                        </div>
                    </div>
                </form>

                <div id="batchGenerateProgress" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar"
                             style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                    </div>
                    <div class="mt-2">
                        <small id="progressText">准备生成...</small>
                    </div>
                </div>

                <div id="batchGenerateResults" style="display: none;">
                    <h6>生成结果：</h6>
                    <div id="resultsContent"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="startBatchGenerate">开始生成</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
