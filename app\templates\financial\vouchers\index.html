{% extends "financial/base.html" %}

{% block title %}记账凭证{% endblock %}

{% block financial_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <!-- 专业财务凭证管理界面 -->
            <div class="card shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="mb-0 text-dark">记账凭证</h5>
                            <small class="text-muted">Accounting Vouchers</small>
                        </div>
                        <div class="col-md-6 text-right">
                            <!-- 简洁的操作按钮 -->
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('financial.create_voucher') }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus"></i> 新建
                                </a>
                                <a href="{{ url_for('financial.pending_stock_ins_for_voucher') }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-magic"></i> 自动生成
                                </a>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="showBatchModal()">
                                    <i class="fas fa-tasks"></i> 批量
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="exportVouchers()">
                                    <i class="fas fa-download"></i> 导出
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <!-- 专业的统计信息条 -->
                    <div class="bg-light border-bottom px-3 py-2">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <small class="text-muted d-block">总计</small>
                                <strong class="text-primary">{{ vouchers.total }}</strong>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted d-block">待审核</small>
                                <strong class="text-warning">{{ pending_count or 0 }}</strong>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted d-block">已审核</small>
                                <strong class="text-success">{{ approved_count or 0 }}</strong>
                            </div>
                            <div class="col-md-3">
                                <small class="text-muted d-block">本月金额</small>
                                <strong class="text-info">¥{{ "{:,.2f}".format(month_amount or 0) }}</strong>
                            </div>
                        </div>
                    </div>

                    <!-- 专业的搜索筛选区 -->
                    <div class="px-3 py-2 border-bottom bg-light">
                        <form method="GET" id="searchForm">
                            <div class="row align-items-end">
                                <div class="col-md-3">
                                    <label class="form-label text-muted small mb-1">搜索</label>
                                    <input type="text" class="form-control form-control-sm" name="keyword"
                                           value="{{ keyword }}" placeholder="凭证号/摘要">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label text-muted small mb-1">类型</label>
                                    <select class="form-control form-control-sm" name="voucher_type">
                                        <option value="">全部</option>
                                        <option value="收款凭证" {% if voucher_type == '收款凭证' %}selected{% endif %}>收款凭证</option>
                                        <option value="付款凭证" {% if voucher_type == '付款凭证' %}selected{% endif %}>付款凭证</option>
                                        <option value="转账凭证" {% if voucher_type == '转账凭证' %}selected{% endif %}>转账凭证</option>
                                        <option value="记账凭证" {% if voucher_type == '记账凭证' %}selected{% endif %}>记账凭证</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label text-muted small mb-1">状态</label>
                                    <select class="form-control form-control-sm" name="status">
                                        <option value="">全部</option>
                                        <option value="草稿" {% if status == '草稿' %}selected{% endif %}>草稿</option>
                                        <option value="待审核" {% if status == '待审核' %}selected{% endif %}>待审核</option>
                                        <option value="已审核" {% if status == '已审核' %}selected{% endif %}>已审核</option>
                                        <option value="已记账" {% if status == '已记账' %}selected{% endif %}>已记账</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label text-muted small mb-1">开始日期</label>
                                    <input type="date" class="form-control form-control-sm" name="start_date"
                                           value="{{ start_date }}">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label text-muted small mb-1">结束日期</label>
                                    <input type="date" class="form-control form-control-sm" name="end_date"
                                           value="{{ end_date }}">
                                </div>
                                <div class="col-md-1">
                                    <button type="submit" class="btn btn-primary btn-sm w-100">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 专业的凭证列表 -->
                    {% if vouchers.items %}
                    <div class="table-responsive">
                        <table class="table table-sm mb-0" id="vouchersTable">
                            <thead class="bg-light">
                                <tr>
                                    <th style="width: 30px;">
                                        <input type="checkbox" id="selectAll">
                                    </th>
                                    <th style="width: 150px;">凭证号</th>
                                    <th style="width: 60px;">日期</th>
                                    <th style="width: 60px;">类型</th>
                                    <th>摘要</th>
                                    <th style="width: 80px;">金额</th>
                                    <th style="width: 60px;">状态</th>
                                    <th style="width: 120px;">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for voucher in vouchers.items %}
                                <tr class="border-bottom" data-voucher-id="{{ voucher.id }}">
                                    <td class="checkbox-cell">
                                        <input type="checkbox" class="voucher-checkbox" value="{{ voucher.id }}">
                                    </td>
                                    <td style="width: 150px; max-width: 150px;">
                                        <a href="{{ url_for('financial.view_voucher', id=voucher.id) }}">
                                            {{ voucher.voucher_number }}
                                        </a>
                                        {% if voucher.attachment_count and voucher.attachment_count > 0 %}
                                            <i class="fas fa-paperclip" title="有附件" style="font-size: 10px; color: #999; margin-left: 2px;"></i>
                                        {% endif %}
                                    </td>
                                    <td style="text-align: center; color: #666;">
                                        {{ voucher.voucher_date.strftime('%m-%d') }}
                                    </td>
                                    <td style="text-align: center;">
                                        <span style="font-size: 11px; color: #666;">{{ voucher.voucher_type }}</span>
                                    </td>
                                    <td>
                                        <div style="max-width: 250px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="{{ voucher.summary }}">
                                            {{ voucher.summary }}
                                        </div>
                                    </td>
                                    <td class="amount-cell">
                                        {{ "{:,.2f}".format(voucher.total_amount) }}
                                    </td>
                                    <td style="text-align: center;">
                                        {% if voucher.status == '草稿' %}
                                            <span class="badge badge-secondary">{{ voucher.status }}</span>
                                        {% elif voucher.status == '待审核' %}
                                            <span class="badge badge-warning">{{ voucher.status }}</span>
                                        {% elif voucher.status == '已审核' %}
                                            <span class="badge badge-success">{{ voucher.status }}</span>
                                        {% elif voucher.status == '已记账' %}
                                            <span class="badge badge-primary">{{ voucher.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td style="text-align: center;">
                                        <div class="btn-group">
                                            <!-- 查看按钮 -->
                                            <a href="{{ url_for('financial.view_voucher_unified', id=voucher.id) }}"
                                               class="btn btn-outline-info" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </a>

                                            <!-- 编辑按钮 -->
                                            {% if voucher.status in ['草稿', '待审核'] %}
                                            <a href="{{ url_for('financial.edit_voucher_unified', id=voucher.id) }}"
                                               class="btn btn-outline-warning" title="编辑凭证">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% endif %}

                                            <!-- 文本视图按钮 -->
                                            <a href="{{ url_for('financial.voucher_text_view', id=voucher.id) }}"
                                               class="btn btn-outline-secondary" title="文本视图">
                                                <i class="fas fa-file-alt"></i>
                                            </a>

                                            <!-- 审核按钮 -->
                                            {% if voucher.status == '待审核' %}
                                            <button type="button" class="btn btn-outline-success"
                                                    onclick="reviewVoucher({{ voucher.id }})" title="审核通过">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            {% endif %}

                                            <!-- 复制按钮 -->
                                            <button type="button" class="btn btn-outline-primary"
                                                    onclick="copyVoucher({{ voucher.id }})" title="复制凭证">
                                                <i class="fas fa-copy"></i>
                                            </button>

                                            <!-- 删除按钮 -->
                                            {% if voucher.status in ['草稿', '待审核'] %}
                                            <button type="button" class="btn btn-outline-danger"
                                                    onclick="deleteVoucher({{ voucher.id }})" title="删除凭证">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    {% if vouchers.pages > 1 %}
                    <nav aria-label="凭证分页">
                        <ul class="pagination justify-content-center">
                            {% if vouchers.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('financial.vouchers_index', page=vouchers.prev_num, keyword=keyword, voucher_type=voucher_type, status=status, start_date=start_date, end_date=end_date) }}">上一页</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in vouchers.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != vouchers.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('financial.vouchers_index', page=page_num, keyword=keyword, voucher_type=voucher_type, status=status, start_date=start_date, end_date=end_date) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if vouchers.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('financial.vouchers_index', page=vouchers.next_num, keyword=keyword, voucher_type=voucher_type, status=status, start_date=start_date, end_date=end_date) }}">下一页</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle"></i> 暂无财务凭证数据
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量生成凭证模态框 -->
<div class="modal fade" id="batchGenerateModal" tabindex="-1" role="dialog" aria-labelledby="batchGenerateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchGenerateModalLabel">批量生成财务凭证</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>批量生成说明：</strong>
                    <ul class="mb-0 mt-2">
                        <li>系统将自动查找已财务确认但未生成凭证的入库单</li>
                        <li>为每个入库单生成对应的应付账款和财务凭证</li>
                        <li>生成的凭证将自动审核通过</li>
                        <li>请确保会计科目设置正确（原材料：1402，应付账款：2201）</li>
                    </ul>
                </div>

                <form id="batchGenerateForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="startDate">开始日期</label>
                                <input type="date" class="form-control" id="startDate" name="start_date" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="endDate">结束日期</label>
                                <input type="date" class="form-control" id="endDate" name="end_date" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="autoReview" name="auto_review" checked>
                            <label class="form-check-label" for="autoReview">
                                自动审核生成的凭证
                            </label>
                        </div>
                    </div>
                </form>

                <div id="batchGenerateProgress" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar"
                             style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                    </div>
                    <div class="mt-2">
                        <small id="progressText">准备生成...</small>
                    </div>
                </div>

                <div id="batchGenerateResults" style="display: none;">
                    <h6>生成结果：</h6>
                    <div id="resultsContent"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="startBatchGenerate">开始生成</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block financial_css %}
<style>
/* 用友财务软件风格 - 凭证列表页面 */
body {
    background: #f5f5f5 !important;
}

.container-fluid {
    background: #f5f5f5;
    padding: 8px;
}

.card {
    background: white;
    border: 1px solid #ccc;
    border-radius: 0;
    box-shadow: 2px 2px 8px rgba(0,0,0,0.1);
    margin: 0;
}

.card-header {
    background: linear-gradient(to bottom, #f0f0f0, #e0e0e0);
    border-bottom: 1px solid #ccc;
    padding: 8px 15px;
    font-size: 14px;
    font-weight: bold;
    color: #333;
}

/* 工具栏样式 */
.mb-3 {
    background: #f8f8f8;
    border-bottom: 1px solid #ddd;
    padding: 6px 10px;
    margin-bottom: 0 !important;
    display: flex;
    gap: 6px;
    align-items: center;
    flex-wrap: wrap;
}

.btn {
    background: linear-gradient(to bottom, #ffffff, #e6e6e6);
    border: 1px solid #adadad;
    border-radius: 3px;
    padding: 4px 12px;
    font-size: 12px;
    color: #333;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    min-height: 24px;
    font-weight: normal;
}

.btn:hover {
    background: linear-gradient(to bottom, #f0f0f0, #d0d0d0);
    color: #333;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(to bottom, #4a90e2, #357abd);
    color: white;
    border-color: #357abd;
}

.btn-primary:hover {
    background: linear-gradient(to bottom, #357abd, #2968a3);
    color: white;
}

/* 搜索栏样式 */
.bg-light {
    background: #f0f0f0 !important;
    border-bottom: 1px solid #ddd;
    padding: 8px 15px;
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
    font-size: 12px;
}

.form-label {
    color: #666;
    font-weight: normal;
    white-space: nowrap;
    margin-bottom: 0;
}

.form-control, .form-select {
    border: 1px solid #ccc;
    padding: 2px 5px;
    font-size: 12px;
    background: white;
    border-radius: 2px;
    height: auto;
}

.form-control:focus, .form-select:focus {
    border-color: #4a90e2;
    outline: none;
    box-shadow: 0 0 3px rgba(74, 144, 226, 0.3);
}

/* 表格样式 */
.table-responsive {
    padding: 0;
    background: white;
    overflow: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
    margin: 0;
    background: white;
}

.table th {
    background: linear-gradient(to bottom, #f0f0f0, #e0e0e0) !important;
    border: 1px solid #ccc !important;
    padding: 6px 4px !important;
    text-align: center !important;
    font-weight: normal !important;
    color: #333 !important;
    font-size: 12px !important;
    white-space: nowrap;
}

.table td {
    border: 1px solid #ddd !important;
    padding: 4px 6px !important;
    vertical-align: middle !important;
    background: white !important;
    font-size: 12px !important;
}

.table tbody tr:hover {
    background: #f0f8ff !important;
}

.table tbody tr.selected {
    background: #e0e8f0 !important;
}

/* 凭证号样式 */
.table td a {
    font-family: 'Courier New', monospace;
    font-size: 11px;
    font-weight: normal;
    word-break: break-all;
    white-space: normal;
    line-height: 1.2;
    color: #0066cc;
    text-decoration: none;
}

.table td a:hover {
    color: #004499;
    text-decoration: underline;
}

/* 金额样式 */
.amount-cell {
    font-family: 'Courier New', monospace;
    text-align: right;
    font-size: 11px;
    color: #333;
}

/* 状态徽章样式 */
.badge {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 2px;
    font-size: 10px;
    font-weight: normal;
    border: 1px solid;
    min-width: 40px;
    text-align: center;
}

.badge-secondary {
    background: #fff3cd;
    color: #856404;
    border-color: #ffeaa7;
}

.badge-warning {
    background: #d1ecf1;
    color: #0c5460;
    border-color: #bee5eb;
}

.badge-success {
    background: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

.badge-primary {
    background: #e2e3e5;
    color: #383d41;
    border-color: #d6d8db;
}

/* 操作按钮样式 */
.btn-group {
    display: flex;
    gap: 2px;
    justify-content: center;
}

.btn-group .btn {
    background: #f8f8f8;
    border: 1px solid #ccc;
    padding: 2px 4px;
    font-size: 10px;
    color: #666;
    cursor: pointer;
    border-radius: 2px;
    text-decoration: none;
    min-width: 20px;
    text-align: center;
    min-height: auto;
}

.btn-group .btn:hover {
    background: #e8e8e8;
    color: #333;
    text-decoration: none;
}

.btn-outline-info { color: #17a2b8; }
.btn-outline-warning { color: #ffc107; }
.btn-outline-secondary { color: #6c757d; }
.btn-outline-success { color: #28a745; }
.btn-outline-primary { color: #007bff; }
.btn-outline-danger { color: #dc3545; }

/* 复选框样式 */
.checkbox-cell {
    text-align: center;
    width: 30px;
}

.checkbox-cell input[type="checkbox"] {
    margin: 0;
}

/* 状态栏 */
.card-footer {
    background: #f0f0f0;
    border-top: 1px solid #ddd;
    padding: 4px 15px;
    font-size: 11px;
    color: #666;
}
</style>
{% endblock %}

{% block financial_js %}
<script>
// 用友风格财务软件 - 凭证列表页面
$(document).ready(function() {
    initUFVoucherList();
    bindUFEvents();
    updateStatusBar();
});

// 初始化用友风格凭证列表
function initUFVoucherList() {
    console.log('初始化用友风格凭证列表');

    // 全选功能
    $('#selectAll').on('change', function() {
        const isChecked = $(this).prop('checked');
        $('.voucher-checkbox').prop('checked', isChecked);
        updateSelectedRows();
        updateStatusBar();
    });

    // 单个复选框
    $('.voucher-checkbox').on('change', function() {
        const $row = $(this).closest('tr');
        if ($(this).prop('checked')) {
            $row.addClass('selected');
        } else {
            $row.removeClass('selected');
        }

        const total = $('.voucher-checkbox').length;
        const checked = $('.voucher-checkbox:checked').length;
        $('#selectAll').prop('checked', total === checked);
        updateStatusBar();
    });

    // 行点击选择
    $('.table tbody tr').on('click', function(e) {
        if (e.target.type !== 'checkbox' && !$(e.target).is('a') && !$(e.target).is('button') && !$(e.target).is('i')) {
            const $checkbox = $(this).find('.voucher-checkbox');
            $checkbox.prop('checked', !$checkbox.prop('checked')).trigger('change');
        }
    });

    // 双击查看详情
    $('.table tbody tr').on('dblclick', function() {
        const voucherId = $(this).find('.voucher-checkbox').val();
        if (voucherId) {
            window.location.href = `/financial/vouchers/${voucherId}`;
        }
    });
}

// 绑定用友风格事件
function bindUFEvents() {
    // 搜索表单自动提交
    $('select[name="voucher_type"], select[name="status"]').on('change', function() {
        $('#searchForm').submit();
    });

    // 回车搜索
    $('input[name="keyword"]').on('keypress', function(e) {
        if (e.which === 13) {
            $('#searchForm').submit();
        }
    });

    // 快捷键支持
    $(document).on('keydown', function(e) {
        // Ctrl+A 全选
        if (e.ctrlKey && e.key === 'a') {
            e.preventDefault();
            $('#selectAll').prop('checked', true).trigger('change');
        }

        // Delete 删除选中
        if (e.key === 'Delete') {
            const selectedIds = getSelectedVoucherIds();
            if (selectedIds.length > 0) {
                batchDeleteVouchers(selectedIds);
            }
        }

        // F5 刷新
        if (e.key === 'F5') {
            e.preventDefault();
            window.location.reload();
        }
    });
}

// 更新选中行样式
function updateSelectedRows() {
    $('.voucher-checkbox').each(function() {
        const $row = $(this).closest('tr');
        if ($(this).prop('checked')) {
            $row.addClass('selected');
        } else {
            $row.removeClass('selected');
        }
    });
}

// 更新状态栏
function updateStatusBar() {
    const total = $('.voucher-checkbox').length;
    const selected = $('.voucher-checkbox:checked').length;
    const statusText = selected > 0 ?
        `共 ${total} 条记录，已选择 ${selected} 条` :
        `共 ${total} 条记录`;

    // 如果有状态栏元素，更新它
    if ($('.card-footer').length === 0) {
        $('.card').append(`<div class="card-footer">${statusText}</div>`);
    } else {
        $('.card-footer').text(statusText);
    }
}

// 获取选中的凭证ID
function getSelectedVoucherIds() {
    const ids = [];
    $('.voucher-checkbox:checked').each(function() {
        ids.push($(this).val());
    });
    return ids;
}

// 更新批量操作状态
function updateBatchActions() {
    const checkedCount = $('.voucher-checkbox:checked').length;
    // 这里可以显示/隐藏批量操作按钮
}

// 批量操作模态框
function showBatchModal() {
    const checkedIds = getSelectedIds();
    if (checkedIds.length === 0) {
        alert('请先选择凭证');
        return;
    }
    alert(`已选择 ${checkedIds.length} 个凭证，批量操作功能开发中...`);
}

// 导出凭证
function exportVouchers() {
    alert('导出功能开发中...');
}

// 获取选中的凭证ID
function getSelectedIds() {
    const ids = [];
    $('.voucher-checkbox:checked').each(function() {
        ids.push($(this).val());
    });
    return ids;
}

// 凭证操作
function reviewVoucher(id) {
    if (confirm('确定审核此凭证？')) {
        window.location.href = `/financial/vouchers/${id}/review`;
    }
}

function copyVoucher(id) {
    alert('复制功能开发中...');
}

function deleteVoucher(id) {
    if (confirm('确定删除此凭证？此操作不可恢复！')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/financial/vouchers/${id}/delete`;
        document.body.appendChild(form);
        form.submit();
    }
}

// 显示批量生成模态框
function showBatchGenerateModal() {
    // 设置默认日期范围（本月）
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    document.getElementById('startDate').value = firstDay.toISOString().split('T')[0];
    document.getElementById('endDate').value = lastDay.toISOString().split('T')[0];

    // 重置状态
    document.getElementById('batchGenerateProgress').style.display = 'none';
    document.getElementById('batchGenerateResults').style.display = 'none';
    document.getElementById('batchGenerateForm').style.display = 'block';
    document.getElementById('startBatchGenerate').style.display = 'inline-block';

    $('#batchGenerateModal').modal('show');
}

// 开始批量生成
document.getElementById('startBatchGenerate').addEventListener('click', function() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const autoReview = document.getElementById('autoReview').checked;

    if (!startDate || !endDate) {
        alert('请选择日期范围');
        return;
    }

    if (new Date(startDate) > new Date(endDate)) {
        alert('开始日期不能大于结束日期');
        return;
    }

    // 隐藏表单，显示进度
    document.getElementById('batchGenerateForm').style.display = 'none';
    document.getElementById('startBatchGenerate').style.display = 'none';
    document.getElementById('batchGenerateProgress').style.display = 'block';

    // 开始批量生成
    batchGenerateVouchers(startDate, endDate, autoReview);
});

// 批量生成凭证
function batchGenerateVouchers(startDate, endDate, autoReview) {
    const progressBar = document.querySelector('.progress-bar');
    const progressText = document.getElementById('progressText');

    progressText.textContent = '正在查找待生成凭证的入库单...';
    progressBar.style.width = '10%';
    progressBar.textContent = '10%';

    fetch('{{ url_for("financial.batch_generate_vouchers_from_stock_ins") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            start_date: startDate,
            end_date: endDate,
            auto_review: autoReview
        })
    })
    .then(response => response.json())
    .then(data => {
        progressBar.style.width = '100%';
        progressBar.textContent = '100%';
        progressText.textContent = '生成完成！';

        // 显示结果
        document.getElementById('batchGenerateProgress').style.display = 'none';
        document.getElementById('batchGenerateResults').style.display = 'block';

        const resultsContent = document.getElementById('resultsContent');
        if (data.success) {
            resultsContent.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> 批量生成成功</h6>
                    <p><strong>处理结果：</strong></p>
                    <ul>
                        <li>成功生成凭证：${data.success_count} 个</li>
                        <li>失败：${data.failed_count} 个</li>
                        <li>总金额：¥${data.total_amount.toFixed(2)}</li>
                    </ul>
                    ${data.failed_count > 0 ? '<p><strong>失败原因：</strong>请检查会计科目设置是否正确</p>' : ''}
                </div>
            `;
        } else {
            resultsContent.innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-circle"></i> 批量生成失败</h6>
                    <p>${data.message}</p>
                </div>
            `;
        }

        // 3秒后自动刷新页面
        setTimeout(() => {
            window.location.reload();
        }, 3000);
    })
    .catch(error => {
        progressBar.classList.remove('progress-bar-animated');
        progressBar.classList.add('bg-danger');
        progressText.textContent = '生成失败：' + error.message;

        document.getElementById('batchGenerateResults').style.display = 'block';
        document.getElementById('resultsContent').innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-circle"></i> 网络错误</h6>
                <p>请检查网络连接后重试</p>
            </div>
        `;
    });
}
</script>
{% endblock %}
