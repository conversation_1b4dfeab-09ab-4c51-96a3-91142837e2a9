{% extends "financial/base.html" %}

{% block title %}记账凭证{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
/* 用友风格凭证列表页面样式 */
.voucher-list-container {
    background: #f5f7fa;
    min-height: 100vh;
    padding: 10px;
}

.voucher-list-window {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(30, 136, 229, 0.1);
    margin: 0 auto;
    max-width: 1400px;
}

.voucher-list-header {
    background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
    border-bottom: 1px solid #90caf9;
    padding: 8px 15px;
    font-size: 14px;
    font-weight: bold;
    color: #1565c0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.voucher-list-toolbar {
    background: #f8f8f8;
    border-bottom: 1px solid #e0e0e0;
    padding: 5px 10px;
    display: flex;
    gap: 5px;
    align-items: center;
    flex-wrap: wrap;
}

.toolbar-btn {
    background: linear-gradient(to bottom, #ffffff, #f5f5f5);
    border: 1px solid #e0e0e0;
    border-radius: 3px;
    padding: 4px 8px;
    font-size: 12px;
    color: #333;
    cursor: pointer;
    min-width: 60px;
    text-align: center;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.toolbar-btn:hover {
    background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
    color: #1565c0;
    text-decoration: none;
}

.toolbar-btn.primary {
    background: linear-gradient(to bottom, #1e88e5, #1565c0);
    color: white;
    border-color: #1565c0;
}

.toolbar-btn.primary:hover {
    background: linear-gradient(to bottom, #1565c0, #0d47a1);
    color: white;
}

.voucher-search-bar {
    background: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    padding: 8px 15px;
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
    font-size: 12px;
}

.search-group {
    display: flex;
    align-items: center;
    gap: 5px;
}

.search-label {
    color: #666;
    font-weight: normal;
    white-space: nowrap;
}

.search-input {
    border: 1px solid #e0e0e0;
    padding: 2px 5px;
    font-size: 12px;
    background: white;
    border-radius: 3px;
}

.search-input:focus {
    border-color: #1e88e5;
    box-shadow: 0 0 0 2px rgba(30, 136, 229, 0.1);
    outline: none;
}

.voucher-stats-bar {
    background: #e8f4fd;
    border-bottom: 1px solid #e0e0e0;
    padding: 6px 15px;
    display: flex;
    gap: 30px;
    align-items: center;
    font-size: 12px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.stat-label {
    color: #666;
}

.stat-value {
    font-weight: bold;
    color: #1565c0;
}

.voucher-list-table-container {
    padding: 0;
    background: white;
    overflow: auto;
}

.voucher-list-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
    margin: 0;
}

.voucher-list-table th {
    background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
    border: 1px solid #90caf9;
    padding: 6px 4px;
    text-align: center;
    font-weight: normal;
    color: #1565c0;
    font-size: 12px;
    white-space: nowrap;
}

.voucher-list-table td {
    border: 1px solid #e0e0e0;
    padding: 4px 6px;
    vertical-align: middle;
    background: white;
    font-size: 12px;
}

.voucher-list-table tbody tr:hover {
    background: #f5f5f5;
}

.voucher-list-table tbody tr.selected {
    background: #e3f2fd;
}

.voucher-number-link {
    font-family: 'Courier New', monospace;
    font-size: 11px;
    color: #1565c0;
    text-decoration: none;
    font-weight: normal;
}

.voucher-number-link:hover {
    color: #0d47a1;
    text-decoration: underline;
}

.amount-cell {
    font-family: 'Courier New', monospace;
    text-align: right;
    font-size: 11px;
    color: #333;
}

.status-badge {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: normal;
    border: 1px solid;
    min-width: 40px;
    text-align: center;
}

.status-badge.draft {
    background-color: #e0e0e0;
    color: #616161;
    border-color: #bdbdbd;
}

.status-badge.pending {
    background-color: #fff8e1;
    color: #ff8f00;
    border-color: #ffcc02;
}

.status-badge.approved {
    background-color: #e8f5e9;
    color: #2e7d32;
    border-color: #4caf50;
}

.status-badge.posted {
    background-color: #e3f2fd;
    color: #1565c0;
    border-color: #2196f3;
}

.action-buttons {
    display: flex;
    gap: 2px;
    justify-content: center;
}

.action-btn {
    background: #f8f8f8;
    border: 1px solid #e0e0e0;
    padding: 2px 4px;
    font-size: 10px;
    color: #666;
    cursor: pointer;
    border-radius: 2px;
    text-decoration: none;
    min-width: 20px;
    text-align: center;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    background: #e8e8e8;
    color: #333;
    text-decoration: none;
}

.action-btn.view { color: #1976d2; }
.action-btn.edit { color: #f57c00; }
.action-btn.delete { color: #d32f2f; }

.voucher-pagination {
    background: #f8f8f8;
    border-top: 1px solid #e0e0e0;
    padding: 8px 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
}

.page-link {
    background: linear-gradient(to bottom, #ffffff, #f5f5f5);
    border: 1px solid #e0e0e0;
    border-radius: 3px;
    padding: 4px 8px;
    font-size: 12px;
    color: #333;
    cursor: pointer;
    text-decoration: none;
    min-width: 30px;
    text-align: center;
}

.page-link:hover {
    background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
    color: #1565c0;
    text-decoration: none;
}

.page-link.active {
    background: linear-gradient(to bottom, #1e88e5, #1565c0);
    color: white;
    border-color: #1565c0;
}

.page-link.disabled {
    background: #f5f5f5;
    color: #999;
    cursor: not-allowed;
}

.status-bar {
    background: #f5f5f5;
    border-top: 1px solid #e0e0e0;
    padding: 4px 15px;
    font-size: 11px;
    color: #666;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .voucher-search-bar {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .voucher-stats-bar {
        flex-wrap: wrap;
        gap: 15px;
    }

    .voucher-list-table {
        font-size: 11px;
    }
}
</style>
{% endblock %}

{% block financial_content %}
<div class="voucher-list-container">
    <div class="voucher-list-window">
        <!-- 窗口标题栏 -->
        <div class="voucher-list-header">
            <span>记账凭证管理</span>
            <div class="window-controls">
                <button class="toolbar-btn" onclick="location.href='{{ url_for('financial.reports_index') }}'">📊 报表</button>
                <button class="toolbar-btn" onclick="refreshPage()">🔄 刷新</button>
            </div>
        </div>

        <!-- 工具栏 -->
        <div class="voucher-list-toolbar">
            <a href="{{ url_for('financial.create_voucher') }}" class="toolbar-btn primary">
                ➕ 新建
            </a>
            <a href="{{ url_for('financial.pending_stock_ins_for_voucher') }}" class="toolbar-btn">
                🔮 自动生成
            </a>
            <button type="button" class="toolbar-btn" onclick="showBatchModal()">
                📋 批量
            </button>
            <button type="button" class="toolbar-btn" onclick="exportVouchers()">
                📤 导出
            </button>
            <div style="margin-left: auto;">
                <button type="button" class="toolbar-btn" onclick="showHelp()">
                    ❓ 帮助
                </button>
            </div>
        </div>
        <!-- 统计信息栏 -->
        <div class="voucher-stats-bar">
            <div class="stat-item">
                <span class="stat-label">总计:</span>
                <span class="stat-value">{{ vouchers.total }}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">待审核:</span>
                <span class="stat-value" style="color: #ff8f00;">{{ pending_count or 0 }}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">已审核:</span>
                <span class="stat-value" style="color: #2e7d32;">{{ approved_count or 0 }}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">本月金额:</span>
                <span class="stat-value amount-cell">¥{{ "{:,.2f}".format(month_amount or 0) }}</span>
            </div>
        </div>

        <!-- 搜索筛选区 -->
        <div class="voucher-search-bar">
            <form method="GET" id="searchForm" style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap; width: 100%;">
                <div class="search-group">
                    <span class="search-label">搜索:</span>
                    <input type="text" class="search-input" name="keyword" style="width: 120px;"
                           value="{{ keyword }}" placeholder="凭证号/摘要">
                </div>
                <div class="search-group">
                    <span class="search-label">类型:</span>
                    <select class="search-input" name="voucher_type" style="width: 80px;">
                        <option value="">全部</option>
                        <option value="收款凭证" {% if voucher_type == '收款凭证' %}selected{% endif %}>收款</option>
                        <option value="付款凭证" {% if voucher_type == '付款凭证' %}selected{% endif %}>付款</option>
                        <option value="转账凭证" {% if voucher_type == '转账凭证' %}selected{% endif %}>转账</option>
                        <option value="记账凭证" {% if voucher_type == '记账凭证' %}selected{% endif %}>记账</option>
                    </select>
                </div>
                <div class="search-group">
                    <span class="search-label">状态:</span>
                    <select class="search-input" name="status" style="width: 70px;">
                        <option value="">全部</option>
                        <option value="草稿" {% if status == '草稿' %}selected{% endif %}>草稿</option>
                        <option value="待审核" {% if status == '待审核' %}selected{% endif %}>待审核</option>
                        <option value="已审核" {% if status == '已审核' %}selected{% endif %}>已审核</option>
                        <option value="已记账" {% if status == '已记账' %}selected{% endif %}>已记账</option>
                    </select>
                </div>
                <div class="search-group">
                    <span class="search-label">开始:</span>
                    <input type="date" class="search-input" name="start_date" style="width: 110px;"
                           value="{{ start_date }}">
                </div>
                <div class="search-group">
                    <span class="search-label">结束:</span>
                    <input type="date" class="search-input" name="end_date" style="width: 110px;"
                           value="{{ end_date }}">
                </div>
                <div class="search-group">
                    <button type="submit" class="toolbar-btn primary">
                        🔍 查询
                    </button>
                    <a href="{{ url_for('financial.vouchers_index') }}" class="toolbar-btn">
                        🔄 重置
                    </a>
                </div>
            </form>
        </div>

        <!-- 凭证列表表格 -->
        {% if vouchers.items %}
        <div class="voucher-list-table-container">
            <table class="voucher-list-table" id="vouchersTable">
                <thead>
                    <tr>
                        <th style="width: 30px;">
                            <input type="checkbox" id="selectAll">
                        </th>
                        <th style="width: 120px;">凭证号</th>
                        <th style="width: 60px;">日期</th>
                        <th style="width: 50px;">类型</th>
                        <th>摘要</th>
                        <th style="width: 80px;">金额</th>
                        <th style="width: 50px;">状态</th>
                        <th style="width: 120px;">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for voucher in vouchers.items %}
                    <tr data-voucher-id="{{ voucher.id }}">
                        <td style="text-align: center;">
                            <input type="checkbox" class="voucher-checkbox" value="{{ voucher.id }}">
                        </td>
                        <td>
                            <a href="{{ url_for('financial.view_voucher', id=voucher.id) }}" class="voucher-number-link">
                                {{ voucher.voucher_number }}
                            </a>
                            {% if voucher.attachment_count and voucher.attachment_count > 0 %}
                                <i class="fas fa-paperclip" title="有附件" style="font-size: 9px; color: #999; margin-left: 2px;"></i>
                            {% endif %}
                        </td>
                        <td style="text-align: center; color: #666; font-size: 11px;">
                            {{ voucher.voucher_date.strftime('%m-%d') }}
                        </td>
                        <td style="text-align: center; font-size: 11px; color: #666;">
                            {{ voucher.voucher_type }}
                        </td>
                        <td>
                            <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="{{ voucher.summary }}">
                                {{ voucher.summary }}
                            </div>
                        </td>
                        <td class="amount-cell">
                            {{ "{:,.2f}".format(voucher.total_amount) }}
                        </td>
                        <td style="text-align: center;">
                            {% if voucher.status == '草稿' %}
                                <span class="status-badge draft">{{ voucher.status }}</span>
                            {% elif voucher.status == '待审核' %}
                                <span class="status-badge pending">{{ voucher.status }}</span>
                            {% elif voucher.status == '已审核' %}
                                <span class="status-badge approved">{{ voucher.status }}</span>
                            {% elif voucher.status == '已记账' %}
                                <span class="status-badge posted">{{ voucher.status }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="action-buttons">
                                <!-- 查看按钮 -->
                                <a href="{{ url_for('financial.view_voucher', id=voucher.id) }}"
                                   class="action-btn view" title="查看详情">
                                    👁
                                </a>

                                <!-- 编辑按钮 -->
                                {% if voucher.status in ['草稿', '待审核'] %}
                                <a href="{{ url_for('financial.edit_voucher', id=voucher.id) }}"
                                   class="action-btn edit" title="编辑凭证">
                                    ✏
                                </a>
                                {% endif %}

                                <!-- 文本视图按钮 -->
                                <a href="{{ url_for('financial.voucher_text_view', id=voucher.id) }}"
                                   class="action-btn" title="文本视图">
                                    📄
                                </a>

                                <!-- 审核按钮 -->
                                {% if voucher.status == '待审核' %}
                                <button type="button" class="action-btn"
                                        onclick="reviewVoucher({{ voucher.id }})" title="审核通过"
                                        style="color: #2e7d32;">
                                    ✓
                                </button>
                                {% endif %}

                                <!-- 复制按钮 -->
                                <button type="button" class="action-btn"
                                        onclick="copyVoucher({{ voucher.id }})" title="复制凭证">
                                    📋
                                </button>

                                <!-- 删除按钮 -->
                                {% if voucher.status in ['草稿', '待审核'] %}
                                <button type="button" class="action-btn delete"
                                        onclick="deleteVoucher({{ voucher.id }})" title="删除凭证">
                                    🗑
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        {% if vouchers.pages > 1 %}
        <div class="voucher-pagination">
            {% if vouchers.has_prev %}
            <a class="page-link" href="{{ url_for('financial.vouchers_index', page=vouchers.prev_num, keyword=keyword, voucher_type=voucher_type, status=status, start_date=start_date, end_date=end_date) }}">◀ 上页</a>
            {% endif %}

            {% for page_num in vouchers.iter_pages() %}
                {% if page_num %}
                    {% if page_num != vouchers.page %}
                    <a class="page-link" href="{{ url_for('financial.vouchers_index', page=page_num, keyword=keyword, voucher_type=voucher_type, status=status, start_date=start_date, end_date=end_date) }}">{{ page_num }}</a>
                    {% else %}
                    <span class="page-link active">{{ page_num }}</span>
                    {% endif %}
                {% else %}
                <span class="page-link disabled">…</span>
                {% endif %}
            {% endfor %}

            {% if vouchers.has_next %}
            <a class="page-link" href="{{ url_for('financial.vouchers_index', page=vouchers.next_num, keyword=keyword, voucher_type=voucher_type, status=status, start_date=start_date, end_date=end_date) }}">下页 ▶</a>
            {% endif %}
        </div>
        {% endif %}
        {% else %}
        <div style="padding: 40px; text-align: center; color: #666; background: white;">
            <div style="font-size: 48px; margin-bottom: 10px;">📋</div>
            <div>暂无财务凭证数据</div>
            <div style="font-size: 12px; margin-top: 5px;">点击"新建"按钮创建第一张凭证</div>
        </div>
        {% endif %}

        <!-- 状态栏 -->
        <div class="status-bar">
            <span>共 {{ vouchers.total }} 条记录</span>
            <span id="current-time"></span>
        </div>
    </div>
</div>

<!-- 批量生成凭证模态框 -->
<div class="modal fade" id="batchGenerateModal" tabindex="-1" role="dialog" aria-labelledby="batchGenerateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchGenerateModalLabel">批量生成财务凭证</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>批量生成说明：</strong>
                    <ul class="mb-0 mt-2">
                        <li>系统将自动查找已财务确认但未生成凭证的入库单</li>
                        <li>为每个入库单生成对应的应付账款和财务凭证</li>
                        <li>生成的凭证将自动审核通过</li>
                        <li>请确保会计科目设置正确（原材料：1402，应付账款：2201）</li>
                    </ul>
                </div>

                <form id="batchGenerateForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="startDate">开始日期</label>
                                <input type="date" class="form-control" id="startDate" name="start_date" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="endDate">结束日期</label>
                                <input type="date" class="form-control" id="endDate" name="end_date" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="autoReview" name="auto_review" checked>
                            <label class="form-check-label" for="autoReview">
                                自动审核生成的凭证
                            </label>
                        </div>
                    </div>
                </form>

                <div id="batchGenerateProgress" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar"
                             style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                    </div>
                    <div class="mt-2">
                        <small id="progressText">准备生成...</small>
                    </div>
                </div>

                <div id="batchGenerateResults" style="display: none;">
                    <h6>生成结果：</h6>
                    <div id="resultsContent"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="startBatchGenerate">开始生成</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}



{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
// 用友风格财务软件 - 凭证列表页面
$(document).ready(function() {
    initUFVoucherList();
    bindUFEvents();
    updateTime();
    setInterval(updateTime, 1000);
});

// 更新时间显示
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleString();
    document.getElementById('current-time').textContent = timeString;
}

// 刷新页面
function refreshPage() {
    window.location.reload();
}

// 显示帮助
function showHelp() {
    alert('用友风格凭证管理系统\n\n快捷键：\nCtrl+A: 全选\nDelete: 删除选中\nF5: 刷新页面\n双击行: 查看详情');
}

// 初始化用友风格凭证列表
function initUFVoucherList() {
    console.log('初始化用友风格凭证列表');

    // 全选功能
    $('#selectAll').on('change', function() {
        const isChecked = $(this).prop('checked');
        $('.voucher-checkbox').prop('checked', isChecked);
        updateSelectedRows();
        updateStatusBar();
    });

    // 单个复选框
    $('.voucher-checkbox').on('change', function() {
        const $row = $(this).closest('tr');
        if ($(this).prop('checked')) {
            $row.addClass('selected');
        } else {
            $row.removeClass('selected');
        }

        const total = $('.voucher-checkbox').length;
        const checked = $('.voucher-checkbox:checked').length;
        $('#selectAll').prop('checked', total === checked);
        updateStatusBar();
    });

    // 行点击选择
    $('.voucher-list-table tbody tr').on('click', function(e) {
        if (e.target.type !== 'checkbox' && !$(e.target).is('a') && !$(e.target).is('button') && !$(e.target).is('i')) {
            const $checkbox = $(this).find('.voucher-checkbox');
            $checkbox.prop('checked', !$checkbox.prop('checked')).trigger('change');
        }
    });

    // 双击查看详情
    $('.voucher-list-table tbody tr').on('dblclick', function() {
        const voucherId = $(this).find('.voucher-checkbox').val();
        if (voucherId) {
            window.location.href = `{{ url_for('financial.view_voucher', id='VOUCHER_ID') }}`.replace('VOUCHER_ID', voucherId);
        }
    });
}

// 绑定用友风格事件
function bindUFEvents() {
    // 搜索表单自动提交
    $('select[name="voucher_type"], select[name="status"]').on('change', function() {
        $('#searchForm').submit();
    });

    // 回车搜索
    $('input[name="keyword"]').on('keypress', function(e) {
        if (e.which === 13) {
            $('#searchForm').submit();
        }
    });

    // 快捷键支持
    $(document).on('keydown', function(e) {
        // Ctrl+A 全选
        if (e.ctrlKey && e.key === 'a') {
            e.preventDefault();
            $('#selectAll').prop('checked', true).trigger('change');
        }

        // Delete 删除选中
        if (e.key === 'Delete') {
            const selectedIds = getSelectedVoucherIds();
            if (selectedIds.length > 0) {
                batchDeleteVouchers(selectedIds);
            }
        }

        // F5 刷新
        if (e.key === 'F5') {
            e.preventDefault();
            window.location.reload();
        }
    });
}

// 更新选中行样式
function updateSelectedRows() {
    $('.voucher-checkbox').each(function() {
        const $row = $(this).closest('tr');
        if ($(this).prop('checked')) {
            $row.addClass('selected');
        } else {
            $row.removeClass('selected');
        }
    });
}

// 更新状态栏
function updateStatusBar() {
    const total = $('.voucher-checkbox').length;
    const selected = $('.voucher-checkbox:checked').length;
    const statusText = selected > 0 ?
        `共 ${total} 条记录，已选择 ${selected} 条` :
        `共 ${total} 条记录`;

    // 更新状态栏文本
    $('.status-bar span:first-child').text(statusText);
}

// 获取选中的凭证ID
function getSelectedVoucherIds() {
    const ids = [];
    $('.voucher-checkbox:checked').each(function() {
        ids.push($(this).val());
    });
    return ids;
}

// 更新批量操作状态
function updateBatchActions() {
    const checkedCount = $('.voucher-checkbox:checked').length;
    // 这里可以显示/隐藏批量操作按钮
}

// 批量操作模态框
function showBatchModal() {
    const checkedIds = getSelectedIds();
    if (checkedIds.length === 0) {
        alert('请先选择凭证');
        return;
    }
    alert(`已选择 ${checkedIds.length} 个凭证，批量操作功能开发中...`);
}

// 导出凭证
function exportVouchers() {
    alert('导出功能开发中...');
}

// 获取选中的凭证ID
function getSelectedIds() {
    const ids = [];
    $('.voucher-checkbox:checked').each(function() {
        ids.push($(this).val());
    });
    return ids;
}

// 凭证操作
function reviewVoucher(id) {
    if (confirm('确定审核此凭证？')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `{{ url_for('financial.review_voucher', id='VOUCHER_ID') }}`.replace('VOUCHER_ID', id);
        document.body.appendChild(form);
        form.submit();
    }
}

function copyVoucher(id) {
    alert('复制功能开发中...');
}

function deleteVoucher(id) {
    if (confirm('确定删除此凭证？此操作不可恢复！')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `{{ url_for('financial.delete_voucher', id='VOUCHER_ID') }}`.replace('VOUCHER_ID', id);
        document.body.appendChild(form);
        form.submit();
    }
}

// 显示批量生成模态框
function showBatchGenerateModal() {
    // 设置默认日期范围（本月）
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    document.getElementById('startDate').value = firstDay.toISOString().split('T')[0];
    document.getElementById('endDate').value = lastDay.toISOString().split('T')[0];

    // 重置状态
    document.getElementById('batchGenerateProgress').style.display = 'none';
    document.getElementById('batchGenerateResults').style.display = 'none';
    document.getElementById('batchGenerateForm').style.display = 'block';
    document.getElementById('startBatchGenerate').style.display = 'inline-block';

    $('#batchGenerateModal').modal('show');
}

// 开始批量生成
document.getElementById('startBatchGenerate').addEventListener('click', function() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const autoReview = document.getElementById('autoReview').checked;

    if (!startDate || !endDate) {
        alert('请选择日期范围');
        return;
    }

    if (new Date(startDate) > new Date(endDate)) {
        alert('开始日期不能大于结束日期');
        return;
    }

    // 隐藏表单，显示进度
    document.getElementById('batchGenerateForm').style.display = 'none';
    document.getElementById('startBatchGenerate').style.display = 'none';
    document.getElementById('batchGenerateProgress').style.display = 'block';

    // 开始批量生成
    batchGenerateVouchers(startDate, endDate, autoReview);
});

// 批量生成凭证
function batchGenerateVouchers(startDate, endDate, autoReview) {
    const progressBar = document.querySelector('.progress-bar');
    const progressText = document.getElementById('progressText');

    progressText.textContent = '正在查找待生成凭证的入库单...';
    progressBar.style.width = '10%';
    progressBar.textContent = '10%';

    fetch('{{ url_for("financial.batch_generate_vouchers_from_stock_ins") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            start_date: startDate,
            end_date: endDate,
            auto_review: autoReview
        })
    })
    .then(response => response.json())
    .then(data => {
        progressBar.style.width = '100%';
        progressBar.textContent = '100%';
        progressText.textContent = '生成完成！';

        // 显示结果
        document.getElementById('batchGenerateProgress').style.display = 'none';
        document.getElementById('batchGenerateResults').style.display = 'block';

        const resultsContent = document.getElementById('resultsContent');
        if (data.success) {
            resultsContent.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> 批量生成成功</h6>
                    <p><strong>处理结果：</strong></p>
                    <ul>
                        <li>成功生成凭证：${data.success_count} 个</li>
                        <li>失败：${data.failed_count} 个</li>
                        <li>总金额：¥${data.total_amount.toFixed(2)}</li>
                    </ul>
                    ${data.failed_count > 0 ? '<p><strong>失败原因：</strong>请检查会计科目设置是否正确</p>' : ''}
                </div>
            `;
        } else {
            resultsContent.innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-circle"></i> 批量生成失败</h6>
                    <p>${data.message}</p>
                </div>
            `;
        }

        // 3秒后自动刷新页面
        setTimeout(() => {
            window.location.reload();
        }, 3000);
    })
    .catch(error => {
        progressBar.classList.remove('progress-bar-animated');
        progressBar.classList.add('bg-danger');
        progressText.textContent = '生成失败：' + error.message;

        document.getElementById('batchGenerateResults').style.display = 'block';
        document.getElementById('resultsContent').innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-circle"></i> 网络错误</h6>
                <p>请检查网络连接后重试</p>
            </div>
        `;
    });
}
</script>
{% endblock %}
