/**
 * 表单验证中文本地化
 */
(function() {
    'use strict';
    
    // Bootstrap 内置表单验证本地化
    document.addEventListener('DOMContentLoaded', function() {
        // 获取所有需要验证的表单
        var forms = document.querySelectorAll('.needs-validation');
        
        // 遍历表单并阻止提交
        Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                    
                    // 显示自定义错误消息
                    var invalidFields = form.querySelectorAll(':invalid');
                    invalidFields.forEach(function(field) {
                        // 根据验证类型设置自定义消息
                        if (field.validity.valueMissing) {
                            field.setCustomValidity('此字段为必填项');
                        } else if (field.validity.typeMismatch) {
                            if (field.type === 'email') {
                                field.setCustomValidity('请输入有效的电子邮件地址');
                            } else if (field.type === 'url') {
                                field.setCustomValidity('请输入有效的URL');
                            } else {
                                field.setCustomValidity('请输入正确的格式');
                            }
                        } else if (field.validity.tooShort) {
                            field.setCustomValidity('请至少输入 ' + field.minLength + ' 个字符');
                        } else if (field.validity.tooLong) {
                            field.setCustomValidity('请不要超过 ' + field.maxLength + ' 个字符');
                        } else if (field.validity.rangeUnderflow) {
                            field.setCustomValidity('请输入不小于 ' + field.min + ' 的值');
                        } else if (field.validity.rangeOverflow) {
                            field.setCustomValidity('请输入不大于 ' + field.max + ' 的值');
                        } else if (field.validity.patternMismatch) {
                            field.setCustomValidity('请匹配要求的格式');
                        } else {
                            field.setCustomValidity('');
                        }
                    });
                }
                
                form.classList.add('was-validated');
            }, false);
            
            // 在输入时清除自定义验证消息
            form.querySelectorAll('input, select, textarea').forEach(function(input) {
                input.addEventListener('input', function() {
                    this.setCustomValidity('');
                });
            });
        });
    });
    
    // jQuery Validation 插件本地化 (如果存在)
    if (typeof jQuery !== 'undefined' && jQuery.validator) {
        jQuery.extend(jQuery.validator.messages, {
            required: "此字段为必填项",
            remote: "请修正此字段",
            email: "请输入有效的电子邮件地址",
            url: "请输入有效的网址",
            date: "请输入有效的日期",
            dateISO: "请输入有效的日期 (YYYY-MM-DD)",
            number: "请输入有效的数字",
            digits: "只能输入数字",
            creditcard: "请输入有效的信用卡号码",
            equalTo: "你的输入不相同",
            extension: "请输入有效的后缀",
            maxlength: jQuery.validator.format("最多可以输入 {0} 个字符"),
            minlength: jQuery.validator.format("最少要输入 {0} 个字符"),
            rangelength: jQuery.validator.format("请输入长度在 {0} 到 {1} 之间的字符串"),
            range: jQuery.validator.format("请输入范围在 {0} 到 {1} 之间的数值"),
            max: jQuery.validator.format("请输入不大于 {0} 的数值"),
            min: jQuery.validator.format("请输入不小于 {0} 的数值")
        });
    }
})();
