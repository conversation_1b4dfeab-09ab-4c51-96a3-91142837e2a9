/**
 * 移动端表格转卡片工具
 * 自动将表格在移动端转换为卡片视图
 */

(function() {
    'use strict';

    // 移动端检测
    function isMobile() {
        return window.innerWidth <= 768;
    }

    // 创建卡片HTML
    function createCardFromRow(row, headers) {
        const cells = row.querySelectorAll('td');
        if (cells.length === 0) return null;

        const card = document.createElement('div');
        card.className = 'card mb-3 mobile-table-card';
        
        const cardBody = document.createElement('div');
        cardBody.className = 'card-body py-2';

        // 主要信息行
        const mainRow = document.createElement('div');
        mainRow.className = 'row';

        // 左侧主要信息
        const leftCol = document.createElement('div');
        leftCol.className = 'col-8';
        
        // 右侧状态/操作
        const rightCol = document.createElement('div');
        rightCol.className = 'col-4 text-right';

        // 处理每个单元格
        cells.forEach((cell, index) => {
            const header = headers[index];
            const content = cell.innerHTML.trim();
            
            if (content === '' || content === '-') return;

            const fieldDiv = document.createElement('div');
            fieldDiv.className = 'mobile-field';

            // 根据列类型决定放置位置
            if (index === 0 || header.includes('名称') || header.includes('标题')) {
                // 主要信息放左侧
                const title = document.createElement('h6');
                title.className = 'mb-1';
                title.innerHTML = content;
                leftCol.appendChild(title);
            } else if (header.includes('状态') || header.includes('操作')) {
                // 状态和操作放右侧
                rightCol.innerHTML += content;
            } else {
                // 其他信息作为详细信息
                const label = document.createElement('small');
                label.className = 'text-muted d-block';
                label.textContent = header;
                
                const value = document.createElement('div');
                value.innerHTML = content;
                
                fieldDiv.appendChild(label);
                fieldDiv.appendChild(value);
                cardBody.appendChild(fieldDiv);
            }
        });

        mainRow.appendChild(leftCol);
        mainRow.appendChild(rightCol);
        cardBody.insertBefore(mainRow, cardBody.firstChild);
        card.appendChild(cardBody);

        return card;
    }

    // 转换表格为卡片
    function convertTableToCards(table) {
        const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.textContent.trim());
        const rows = table.querySelectorAll('tbody tr');
        
        const cardsContainer = document.createElement('div');
        cardsContainer.className = 'mobile-cards-container d-md-none';

        rows.forEach(row => {
            const card = createCardFromRow(row, headers);
            if (card) {
                cardsContainer.appendChild(card);
            }
        });

        // 插入卡片容器
        table.parentNode.insertBefore(cardsContainer, table.nextSibling);
        
        // 隐藏原表格在移动端
        table.parentNode.classList.add('d-none', 'd-md-block');
    }

    // 自动转换所有表格
    function autoConvertTables() {
        if (!isMobile()) return;

        const tables = document.querySelectorAll('table.table:not(.no-mobile-convert)');
        tables.forEach(convertTableToCards);
    }

    // 响应式处理
    function handleResize() {
        const mobileContainers = document.querySelectorAll('.mobile-cards-container');
        const tableContainers = document.querySelectorAll('.table-responsive');

        if (isMobile()) {
            mobileContainers.forEach(container => {
                container.classList.remove('d-none');
                container.classList.add('d-md-none');
            });
            tableContainers.forEach(container => {
                container.classList.add('d-none', 'd-md-block');
            });
        } else {
            mobileContainers.forEach(container => {
                container.classList.add('d-none');
            });
            tableContainers.forEach(container => {
                container.classList.remove('d-none', 'd-md-block');
            });
        }
    }

    // 初始化
    function init() {
        // DOM加载完成后执行
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', autoConvertTables);
        } else {
            autoConvertTables();
        }

        // 监听窗口大小变化
        window.addEventListener('resize', handleResize);
    }

    // 导出到全局
    window.MobileTableCards = {
        init: init,
        convertTable: convertTableToCards,
        isMobile: isMobile
    };

    // 自动初始化
    init();

})();
