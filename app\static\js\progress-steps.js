/**
 * 步骤进度条增强功能
 */

class ProgressSteps {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? document.querySelector(container) : container;
        this.options = {
            animationDuration: 300,
            autoResize: true,
            responsive: true,
            ...options
        };
        
        this.init();
    }
    
    init() {
        if (!this.container) return;
        
        this.setupResponsive();
        this.setupAnimation();
        
        if (this.options.autoResize) {
            this.setupAutoResize();
        }
    }
    
    /**
     * 设置响应式行为
     */
    setupResponsive() {
        if (!this.options.responsive) return;
        
        const stepLabels = this.container.querySelector('.step-labels');
        if (!stepLabels) return;
        
        const checkLayout = () => {
            const containerWidth = this.container.offsetWidth;
            const labels = stepLabels.querySelectorAll('.step-label');
            
            if (containerWidth < 768) {
                // 移动端布局
                stepLabels.classList.add('step-labels-grid');
                labels.forEach(label => {
                    label.style.fontSize = '0.7rem';
                });
            } else {
                // 桌面端布局
                stepLabels.classList.remove('step-labels-grid');
                labels.forEach(label => {
                    label.style.fontSize = '';
                });
            }
        };
        
        checkLayout();
        window.addEventListener('resize', checkLayout);
    }
    
    /**
     * 设置动画效果
     */
    setupAnimation() {
        const progressBar = this.container.querySelector('.progress-bar');
        if (!progressBar) return;
        
        // 添加动画类
        progressBar.classList.add('animated');
        
        // 设置CSS变量用于动画
        const width = progressBar.style.width;
        if (width) {
            progressBar.style.setProperty('--progress-width', width);
        }
    }
    
    /**
     * 设置自动调整大小
     */
    setupAutoResize() {
        const resizeObserver = new ResizeObserver(entries => {
            for (let entry of entries) {
                this.adjustLayout(entry.target);
            }
        });
        
        resizeObserver.observe(this.container);
    }
    
    /**
     * 调整布局
     */
    adjustLayout(container) {
        const stepLabels = container.querySelector('.step-labels');
        if (!stepLabels) return;
        
        const labels = stepLabels.querySelectorAll('.step-label');
        const containerWidth = container.offsetWidth;
        const labelCount = labels.length;
        const minLabelWidth = 80; // 最小标签宽度
        
        if (containerWidth < labelCount * minLabelWidth) {
            // 空间不足，使用垂直布局
            stepLabels.style.flexDirection = 'column';
            stepLabels.style.alignItems = 'flex-start';
            
            labels.forEach(label => {
                label.style.textAlign = 'left';
                label.style.marginBottom = '0.25rem';
            });
        } else {
            // 空间充足，使用水平布局
            stepLabels.style.flexDirection = '';
            stepLabels.style.alignItems = '';
            
            labels.forEach(label => {
                label.style.textAlign = 'center';
                label.style.marginBottom = '';
            });
        }
    }
    
    /**
     * 更新进度
     */
    updateProgress(currentStep, totalSteps) {
        const progressBar = this.container.querySelector('.progress-bar');
        const stepLabels = this.container.querySelectorAll('.step-label');
        
        if (progressBar) {
            const percentage = (currentStep / totalSteps) * 100;
            progressBar.style.width = `${percentage}%`;
            progressBar.setAttribute('aria-valuenow', percentage);
        }
        
        // 更新步骤标签状态
        stepLabels.forEach((label, index) => {
            const stepNumber = index + 1;
            
            label.classList.remove('active', 'completed', 'pending');
            
            if (stepNumber < currentStep) {
                label.classList.add('completed');
            } else if (stepNumber === currentStep) {
                label.classList.add('active');
            } else {
                label.classList.add('pending');
            }
        });
    }
    
    /**
     * 添加步骤点击事件
     */
    addStepClickHandler(callback) {
        const stepLabels = this.container.querySelectorAll('.step-label');
        
        stepLabels.forEach((label, index) => {
            label.style.cursor = 'pointer';
            label.addEventListener('click', () => {
                if (callback) {
                    callback(index + 1, label);
                }
            });
        });
    }
    
    /**
     * 销毁实例
     */
    destroy() {
        // 移除事件监听器
        window.removeEventListener('resize', this.checkLayout);
        
        // 清理DOM
        const progressBar = this.container.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.classList.remove('animated');
        }
    }
}

/**
 * 自动初始化页面上的所有进度条
 */
function initProgressSteps() {
    const progressContainers = document.querySelectorAll('.progress-steps');
    const instances = [];
    
    progressContainers.forEach(container => {
        const instance = new ProgressSteps(container);
        instances.push(instance);
    });
    
    return instances;
}

/**
 * 工具函数：创建进度条HTML
 */
function createProgressStepsHTML(currentStep, totalSteps, steps, options = {}) {
    const {
        title = '流程进度',
        type = 'default', // default, compact, circular, breadcrumb
        className = ''
    } = options;
    
    const percentage = (currentStep / totalSteps) * 100;
    
    let html = `<div class="progress-steps ${className}">`;
    
    // 标题和步骤信息
    html += `
        <div class="d-flex justify-content-between align-items-center mb-2">
            <small class="text-muted font-weight-bold">${title}</small>
            <small class="text-muted">步骤 ${currentStep}/${totalSteps}</small>
        </div>
    `;
    
    // 进度条
    html += `
        <div class="progress" style="height: 8px;">
            <div class="progress-bar bg-success" 
                 role="progressbar" 
                 style="width: ${percentage}%" 
                 aria-valuenow="${percentage}" 
                 aria-valuemin="0" 
                 aria-valuemax="100">
            </div>
        </div>
    `;
    
    // 步骤标签
    html += '<div class="step-labels d-flex justify-content-between mt-2">';
    
    steps.forEach((step, index) => {
        const stepNumber = index + 1;
        let labelClass = 'step-label';
        
        if (stepNumber < currentStep) {
            labelClass += ' completed text-success';
        } else if (stepNumber === currentStep) {
            labelClass += ' active text-success font-weight-bold';
        } else {
            labelClass += ' pending text-muted';
        }
        
        html += `<small class="${labelClass}">${stepNumber}. ${step}</small>`;
    });
    
    html += '</div></div>';
    
    return html;
}

/**
 * 修复常见的进度条显示问题
 */
function fixProgressStepsDisplay() {
    const progressContainers = document.querySelectorAll('.progress-steps');
    
    progressContainers.forEach(container => {
        // 确保容器有足够的宽度
        if (container.offsetWidth < 300) {
            container.style.minWidth = '300px';
        }
        
        // 修复步骤标签重叠问题
        const stepLabels = container.querySelector('.step-labels');
        if (stepLabels) {
            const labels = stepLabels.querySelectorAll('.step-label');
            const totalWidth = Array.from(labels).reduce((sum, label) => sum + label.offsetWidth, 0);
            
            if (totalWidth > stepLabels.offsetWidth) {
                stepLabels.style.flexWrap = 'wrap';
                stepLabels.style.gap = '0.25rem';
            }
        }
    });
}

// DOM加载完成后自动初始化
document.addEventListener('DOMContentLoaded', function() {
    initProgressSteps();
    fixProgressStepsDisplay();
});

// 导出给全局使用
window.ProgressSteps = ProgressSteps;
window.initProgressSteps = initProgressSteps;
window.createProgressStepsHTML = createProgressStepsHTML;
window.fixProgressStepsDisplay = fixProgressStepsDisplay;
