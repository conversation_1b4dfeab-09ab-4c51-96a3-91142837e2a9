/**
 * 简化图表库 - 替代Chart.js
 * 使用原生Canvas API实现基础图表功能
 */

class SimpleChart {
    constructor(canvas, config) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.config = config;
        this.data = config.data;
        this.options = config.options || {};
        
        // 设置画布尺寸
        this.setCanvasSize();
        
        // 绘制图表
        this.render();
    }
    
    setCanvasSize() {
        const rect = this.canvas.getBoundingClientRect();
        const dpr = window.devicePixelRatio || 1;
        
        this.canvas.width = rect.width * dpr;
        this.canvas.height = rect.height * dpr;
        
        this.ctx.scale(dpr, dpr);
        this.canvas.style.width = rect.width + 'px';
        this.canvas.style.height = rect.height + 'px';
    }
    
    render() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        switch (this.config.type) {
            case 'line':
                this.drawLineChart();
                break;
            case 'doughnut':
                this.drawDoughnutChart();
                break;
            case 'bar':
                this.drawBarChart();
                break;
        }
    }
    
    drawLineChart() {
        const padding = 40;
        const width = this.canvas.clientWidth - padding * 2;
        const height = this.canvas.clientHeight - padding * 2;
        
        const data = this.data.datasets[0].data;
        const labels = this.data.labels;
        const maxValue = Math.max(...data);
        const minValue = Math.min(...data);
        const range = maxValue - minValue;
        
        // 绘制网格线
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        this.ctx.lineWidth = 1;
        
        // 水平网格线
        for (let i = 0; i <= 5; i++) {
            const y = padding + (height / 5) * i;
            this.ctx.beginPath();
            this.ctx.moveTo(padding, y);
            this.ctx.lineTo(padding + width, y);
            this.ctx.stroke();
        }
        
        // 垂直网格线
        for (let i = 0; i <= labels.length - 1; i++) {
            const x = padding + (width / (labels.length - 1)) * i;
            this.ctx.beginPath();
            this.ctx.moveTo(x, padding);
            this.ctx.lineTo(x, padding + height);
            this.ctx.stroke();
        }
        
        // 绘制数据线
        this.ctx.strokeStyle = this.data.datasets[0].borderColor || '#00BFFF';
        this.ctx.lineWidth = 3;
        this.ctx.beginPath();
        
        data.forEach((value, index) => {
            const x = padding + (width / (data.length - 1)) * index;
            const y = padding + height - ((value - minValue) / range) * height;
            
            if (index === 0) {
                this.ctx.moveTo(x, y);
            } else {
                this.ctx.lineTo(x, y);
            }
        });
        
        this.ctx.stroke();
        
        // 绘制数据点
        this.ctx.fillStyle = this.data.datasets[0].borderColor || '#00BFFF';
        data.forEach((value, index) => {
            const x = padding + (width / (data.length - 1)) * index;
            const y = padding + height - ((value - minValue) / range) * height;
            
            this.ctx.beginPath();
            this.ctx.arc(x, y, 4, 0, Math.PI * 2);
            this.ctx.fill();
        });
        
        // 绘制标签
        this.ctx.fillStyle = '#9CA3AF';
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'center';
        
        labels.forEach((label, index) => {
            const x = padding + (width / (labels.length - 1)) * index;
            this.ctx.fillText(label, x, padding + height + 20);
        });
    }
    
    drawDoughnutChart() {
        const centerX = this.canvas.clientWidth / 2;
        const centerY = this.canvas.clientHeight / 2;
        const radius = Math.min(centerX, centerY) - 40;
        const innerRadius = radius * 0.6;
        
        const data = this.data.datasets[0].data;
        const colors = this.data.datasets[0].backgroundColor;
        const total = data.reduce((sum, value) => sum + value, 0);
        
        let currentAngle = -Math.PI / 2;
        
        data.forEach((value, index) => {
            const sliceAngle = (value / total) * Math.PI * 2;
            
            // 绘制扇形
            this.ctx.fillStyle = colors[index];
            this.ctx.beginPath();
            this.ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
            this.ctx.arc(centerX, centerY, innerRadius, currentAngle + sliceAngle, currentAngle, true);
            this.ctx.closePath();
            this.ctx.fill();
            
            currentAngle += sliceAngle;
        });
        
        // 绘制图例
        const legendY = centerY + radius + 20;
        const legendItemWidth = 80;
        const startX = centerX - (data.length * legendItemWidth) / 2;
        
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'left';
        
        data.forEach((value, index) => {
            const x = startX + index * legendItemWidth;
            
            // 颜色块
            this.ctx.fillStyle = colors[index];
            this.ctx.fillRect(x, legendY, 12, 12);
            
            // 标签
            this.ctx.fillStyle = '#9CA3AF';
            this.ctx.fillText(this.data.labels[index], x + 16, legendY + 10);
        });
    }
    
    drawBarChart() {
        const padding = 40;
        const width = this.canvas.clientWidth - padding * 2;
        const height = this.canvas.clientHeight - padding * 2;
        
        const data = this.data.datasets[0].data;
        const labels = this.data.labels;
        const maxValue = Math.max(...data);
        const barWidth = width / data.length * 0.6;
        const barSpacing = width / data.length * 0.4;
        
        // 绘制网格线
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        this.ctx.lineWidth = 1;
        
        for (let i = 0; i <= 5; i++) {
            const y = padding + (height / 5) * i;
            this.ctx.beginPath();
            this.ctx.moveTo(padding, y);
            this.ctx.lineTo(padding + width, y);
            this.ctx.stroke();
        }
        
        // 绘制柱状图
        this.ctx.fillStyle = this.data.datasets[0].backgroundColor || '#9D4EDD';
        
        data.forEach((value, index) => {
            const barHeight = (value / maxValue) * height;
            const x = padding + index * (barWidth + barSpacing) + barSpacing / 2;
            const y = padding + height - barHeight;
            
            this.ctx.fillRect(x, y, barWidth, barHeight);
        });
        
        // 绘制标签
        this.ctx.fillStyle = '#9CA3AF';
        this.ctx.font = '10px Arial';
        this.ctx.textAlign = 'center';
        
        labels.forEach((label, index) => {
            const x = padding + index * (barWidth + barSpacing) + barSpacing / 2 + barWidth / 2;
            this.ctx.save();
            this.ctx.translate(x, padding + height + 30);
            this.ctx.rotate(-Math.PI / 4);
            this.ctx.fillText(label, 0, 0);
            this.ctx.restore();
        });
    }
}

// 全局Chart对象，兼容Chart.js API
window.Chart = function(canvas, config) {
    if (typeof canvas === 'string') {
        canvas = document.getElementById(canvas);
    }
    return new SimpleChart(canvas, config);
};

// 添加响应式支持
window.addEventListener('resize', function() {
    // 重新绘制所有图表
    document.querySelectorAll('canvas').forEach(canvas => {
        if (canvas._chart) {
            canvas._chart.setCanvasSize();
            canvas._chart.render();
        }
    });
});

// 图表数据生成器
window.ChartDataGenerator = {
    // 生成折线图数据
    generateLineData: function() {
        return {
            labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
            datasets: [{
                label: '运营效率',
                data: [85, 88, 90, 87, 92, 89, 91],
                borderColor: '#00BFFF',
                backgroundColor: 'rgba(0, 191, 255, 0.1)',
                tension: 0.4,
                fill: true
            }]
        };
    },
    
    // 生成环形图数据
    generateDoughnutData: function() {
        return {
            labels: ['合格', '待改进', '不合格'],
            datasets: [{
                data: [95, 4, 1],
                backgroundColor: [
                    '#00FF9D',
                    '#FFD700',
                    '#FF4444'
                ]
            }]
        };
    },
    
    // 生成柱状图数据
    generateBarData: function() {
        return {
            labels: ['菜品质量', '服务态度', '环境卫生', '价格合理', '营养均衡'],
            datasets: [{
                label: '满意度',
                data: [92, 88, 90, 85, 87],
                backgroundColor: '#9D4EDD'
            }]
        };
    }
};

// 图表配置生成器
window.ChartConfigGenerator = {
    getLineConfig: function() {
        return {
            type: 'line',
            data: ChartDataGenerator.generateLineData(),
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        };
    },
    
    getDoughnutConfig: function() {
        return {
            type: 'doughnut',
            data: ChartDataGenerator.generateDoughnutData(),
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        };
    },
    
    getBarConfig: function() {
        return {
            type: 'bar',
            data: ChartDataGenerator.generateBarData(),
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        };
    }
};
